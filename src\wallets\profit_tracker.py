"""
Professional-grade profit tracking and portfolio management system
"""
import time
import logging
from decimal import Decimal
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from collections import defaultdict, deque

logger = logging.getLogger(__name__)

@dataclass
class TradeRecord:
    """Individual trade record"""
    timestamp: float
    symbol: str
    side: str  # 'Buy' or 'Sell'
    quantity: float
    price: float
    fee: float
    order_id: str
    profit_loss: float = 0.0
    strategy: str = "unknown"

@dataclass
class PortfolioMetrics:
    """Portfolio performance metrics"""
    total_profit: Decimal = Decimal("0")
    total_trades: int = 0
    winning_trades: int = 0
    losing_trades: int = 0
    win_rate: float = 0.0
    average_profit: Decimal = Decimal("0")
    max_drawdown: Decimal = Decimal("0")
    sharpe_ratio: float = 0.0
    profit_velocity: Decimal = Decimal("0")  # Profit per minute
    
class ProfitTracker:
    """Enterprise-grade profit tracking system"""
    
    def __init__(self):
        self.trades: List[TradeRecord] = []
        self.portfolio_history: deque = deque(maxlen=1000)
        self.strategy_performance: Dict[str, Dict] = defaultdict(dict)
        self.start_time = time.time()
        self.initial_balance = Decimal("0")
        self.current_balance = Decimal("0")
        
        logger.info("🏦 [PROFIT-TRACKER] Professional profit tracking system initialized")
    
    def record_trade(self, trade: TradeRecord):
        """Record a new trade"""
        self.trades.append(trade)
        self._update_strategy_performance(trade)
        logger.info(f"📊 [TRADE-RECORD] {trade.symbol} {trade.side} {trade.quantity} @ {trade.price}")
    
    def _update_strategy_performance(self, trade: TradeRecord):
        """Update strategy-specific performance metrics"""
        strategy = trade.strategy
        if strategy not in self.strategy_performance:
            self.strategy_performance[strategy] = {
                'total_profit': Decimal("0"),
                'trade_count': 0,
                'win_count': 0,
                'last_trade_time': 0
            }
        
        perf = self.strategy_performance[strategy]
        perf['total_profit'] += Decimal(str(trade.profit_loss))
        perf['trade_count'] += 1
        if trade.profit_loss > 0:
            perf['win_count'] += 1
        perf['last_trade_time'] = trade.timestamp
    
    def get_portfolio_metrics(self) -> PortfolioMetrics:
        """Calculate comprehensive portfolio metrics"""
        if not self.trades:
            return PortfolioMetrics()
        
        total_profit = sum(Decimal(str(trade.profit_loss)) for trade in self.trades)
        total_trades = len(self.trades)
        winning_trades = sum(1 for trade in self.trades if trade.profit_loss > 0)
        losing_trades = total_trades - winning_trades
        
        win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
        average_profit = total_profit / total_trades if total_trades > 0 else Decimal("0")
        
        # Calculate profit velocity (profit per minute)
        elapsed_minutes = (time.time() - self.start_time) / 60
        profit_velocity = total_profit / Decimal(str(elapsed_minutes)) if elapsed_minutes > 0 else Decimal("0")
        
        return PortfolioMetrics(
            total_profit=total_profit,
            total_trades=total_trades,
            winning_trades=winning_trades,
            losing_trades=losing_trades,
            win_rate=win_rate,
            average_profit=average_profit,
            profit_velocity=profit_velocity
        )
    
    def get_strategy_rankings(self) -> List[Dict]:
        """Get strategies ranked by performance"""
        rankings = []
        for strategy, perf in self.strategy_performance.items():
            win_rate = (perf['win_count'] / perf['trade_count'] * 100) if perf['trade_count'] > 0 else 0
            rankings.append({
                'strategy': strategy,
                'profit': float(perf['total_profit']),
                'trades': perf['trade_count'],
                'win_rate': win_rate,
                'last_active': perf['last_trade_time']
            })
        
        return sorted(rankings, key=lambda x: x['profit'], reverse=True)
    
    def update_balance(self, new_balance: Decimal):
        """Update current balance and track portfolio history"""
        self.current_balance = new_balance
        if self.initial_balance == Decimal("0"):
            self.initial_balance = new_balance
        
        self.portfolio_history.append({
            'timestamp': time.time(),
            'balance': float(new_balance),
            'profit': float(new_balance - self.initial_balance)
        })
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive performance summary"""
        metrics = self.get_portfolio_metrics()
        strategy_rankings = self.get_strategy_rankings()
        
        return {
            'metrics': {
                'total_profit': float(metrics.total_profit),
                'total_trades': metrics.total_trades,
                'win_rate': metrics.win_rate,
                'profit_velocity': float(metrics.profit_velocity),
                'average_profit': float(metrics.average_profit)
            },
            'strategies': strategy_rankings,
            'portfolio': {
                'current_balance': float(self.current_balance),
                'initial_balance': float(self.initial_balance),
                'total_return': float(self.current_balance - self.initial_balance),
                'return_percentage': float((self.current_balance - self.initial_balance) / self.initial_balance * 100) if self.initial_balance > 0 else 0
            },
            'uptime_minutes': (time.time() - self.start_time) / 60
        }

# Global profit tracker instance
profit_tracker = ProfitTracker()
