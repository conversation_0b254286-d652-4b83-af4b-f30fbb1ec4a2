"""
Risk exposure calculation and management
"""

import logging
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from decimal import Decimal
from datetime import datetime, timedelta
from collections import deque

logger = logging.getLogger(__name__)

@dataclass
class RiskMetrics:
    """Risk metrics for a trading position or portfolio"""
    var_95: float  # Value at Risk at 95% confidence
    var_99: float  # Value at Risk at 99% confidence
    cvar_95: float  # Conditional Value at Risk at 95%
    cvar_99: float  # Conditional Value at Risk at 99%
    max_drawdown: float
    sharpe_ratio: float
    volatility: float
    beta: float

@dataclass
class PortfolioRisk:
    """Portfolio-level risk assessment"""
    total_exposure: Decimal
    currency_exposures: Dict[str, Decimal]
    correlation_risk: float
    concentration_risk: float
    liquidity_risk: float
    overall_risk_score: float

class CVaRCalculator:
    """Conditional Value at Risk (CVaR) calculator for risk management"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.confidence_levels = config.get('confidence_levels', [0.95, 0.99])
        self.lookback_period = config.get('lookback_period', 252)  # Trading days
        self.returns_history = deque(maxlen=self.lookback_period)
        self.price_history = deque(maxlen=self.lookback_period)
        
    def add_return(self, return_value: float):
        """Add a return observation to the history"""
        self.returns_history.append(return_value)
        
    def add_price(self, price: float):
        """Add a price observation to the history"""
        self.price_history.append(price)
        
    def calculate_var(self, confidence_level: float = 0.95) -> float:
        """Calculate Value at Risk at given confidence level"""
        try:
            if len(self.returns_history) < 30:  # Minimum observations
                return 0.0
                
            returns_array = np.array(list(self.returns_history))
            
            # Calculate VaR using historical simulation
            var_percentile = (1 - confidence_level) * 100
            var = np.percentile(returns_array, var_percentile)
            
            return float(var)
            
        except Exception as e:
            logger.error(f"Error calculating VaR: {e}")
            return 0.0
    
    def calculate_cvar(self, confidence_level: float = 0.95) -> float:
        """Calculate Conditional Value at Risk (Expected Shortfall)"""
        try:
            if len(self.returns_history) < 30:
                return 0.0
                
            returns_array = np.array(list(self.returns_history))
            var = self.calculate_var(confidence_level)
            
            # CVaR is the expected value of returns below VaR
            tail_returns = returns_array[returns_array <= var]
            
            if len(tail_returns) == 0:
                return var
                
            cvar = np.mean(tail_returns)
            return float(cvar)
            
        except Exception as e:
            logger.error(f"Error calculating CVaR: {e}")
            return 0.0
    
    def calculate_volatility(self, annualized: bool = True) -> float:
        """Calculate volatility of returns"""
        try:
            if len(self.returns_history) < 10:
                return 0.0
                
            returns_array = np.array(list(self.returns_history))
            volatility = np.std(returns_array)
            
            if annualized:
                # Annualize assuming 252 trading days
                volatility *= np.sqrt(252)
                
            return float(volatility)
            
        except Exception as e:
            logger.error(f"Error calculating volatility: {e}")
            return 0.0
    
    def calculate_max_drawdown(self) -> float:
        """Calculate maximum drawdown from price history"""
        try:
            if len(self.price_history) < 10:
                return 0.0
                
            prices = np.array(list(self.price_history))
            
            # Calculate running maximum
            running_max = np.maximum.accumulate(prices)
            
            # Calculate drawdowns
            drawdowns = (prices - running_max) / running_max
            
            # Maximum drawdown is the most negative value
            max_drawdown = np.min(drawdowns)
            
            return float(abs(max_drawdown))
            
        except Exception as e:
            logger.error(f"Error calculating max drawdown: {e}")
            return 0.0
    
    def calculate_sharpe_ratio(self, risk_free_rate: float = 0.02) -> float:
        """Calculate Sharpe ratio"""
        try:
            if len(self.returns_history) < 30:
                return 0.0
                
            returns_array = np.array(list(self.returns_history))
            
            # Annualized return
            mean_return = np.mean(returns_array) * 252
            
            # Annualized volatility
            volatility = self.calculate_volatility(annualized=True)
            
            if volatility == 0:
                return 0.0
                
            sharpe = (mean_return - risk_free_rate) / volatility
            return float(sharpe)
            
        except Exception as e:
            logger.error(f"Error calculating Sharpe ratio: {e}")
            return 0.0
    
    def get_risk_metrics(self) -> RiskMetrics:
        """Get comprehensive risk metrics"""
        try:
            return RiskMetrics(
                var_95=self.calculate_var(0.95),
                var_99=self.calculate_var(0.99),
                cvar_95=self.calculate_cvar(0.95),
                cvar_99=self.calculate_cvar(0.99),
                max_drawdown=self.calculate_max_drawdown(),
                sharpe_ratio=self.calculate_sharpe_ratio(),
                volatility=self.calculate_volatility(),
                beta=self.calculate_beta()
            )
            
        except Exception as e:
            logger.error(f"Error getting risk metrics: {e}")
            return RiskMetrics(0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0)
    
    def calculate_beta(self, market_returns: Optional[List[float]] = None) -> float:
        """Calculate beta relative to market (simplified implementation)"""
        try:
            if market_returns is None or len(self.returns_history) < 30:
                return 1.0  # Default beta
                
            # Simplified beta calculation
            # In practice, this would use actual market returns
            returns_array = np.array(list(self.returns_history))
            market_array = np.array(market_returns[-len(returns_array):])
            
            if len(market_array) != len(returns_array):
                return 1.0
                
            covariance = np.cov(returns_array, market_array)[0, 1]
            market_variance = np.var(market_array)
            
            if market_variance == 0:
                return 1.0
                
            beta = covariance / market_variance
            return float(beta)
            
        except Exception as e:
            logger.error(f"Error calculating beta: {e}")
            return 1.0
    
    def assess_position_risk(self, position_size: Decimal, current_price: float) -> Dict[str, float]:
        """Assess risk for a specific position"""
        try:
            position_value = float(position_size) * current_price
            
            var_95 = self.calculate_var(0.95)
            cvar_95 = self.calculate_cvar(0.95)
            
            # Calculate potential losses
            var_loss = abs(var_95 * position_value)
            cvar_loss = abs(cvar_95 * position_value)
            
            return {
                'position_value': position_value,
                'var_95_loss': var_loss,
                'cvar_95_loss': cvar_loss,
                'risk_score': min(cvar_loss / max(position_value, 1), 1.0)
            }
            
        except Exception as e:
            logger.error(f"Error assessing position risk: {e}")
            return {'position_value': 0.0, 'var_95_loss': 0.0, 'cvar_95_loss': 0.0, 'risk_score': 0.0}


class ExposureManager:
    """Manages trading exposure and risk limits"""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        self.risk_calculator = CVaRCalculator(self.config)

        # Default exposure limits
        self.max_portfolio_exposure = self.config.get('max_portfolio_exposure', 0.95)
        self.max_single_position = self.config.get('max_single_position', 0.20)
        self.max_sector_exposure = self.config.get('max_sector_exposure', 0.40)

        # Current exposures
        self.current_exposures = {}
        self.position_limits = {}

    def calculate_portfolio_exposure(self, positions: Dict[str, Any]) -> Dict[str, float]:
        """Calculate current portfolio exposure"""
        try:
            total_value = sum(pos.get('value', 0) for pos in positions.values())
            if total_value == 0:
                return {'total_exposure': 0.0, 'positions': {}}

            exposures = {}
            for symbol, position in positions.items():
                position_value = position.get('value', 0)
                exposures[symbol] = position_value / total_value

            return {
                'total_exposure': sum(exposures.values()),
                'positions': exposures,
                'total_value': total_value
            }

        except Exception as e:
            self.logger.error(f"Error calculating portfolio exposure: {e}")
            return {'total_exposure': 0.0, 'positions': {}}

    def check_exposure_limits(self, symbol: str, proposed_position_size: float,
                            current_positions: Dict[str, Any]) -> Dict[str, Any]:
        """Check if proposed position violates exposure limits"""
        try:
            # Calculate current exposures
            current_exposure = self.calculate_portfolio_exposure(current_positions)
            total_value = current_exposure.get('total_value', 0)

            # Calculate proposed position exposure
            if total_value > 0:
                proposed_exposure = proposed_position_size / total_value
            else:
                proposed_exposure = 1.0 if proposed_position_size > 0 else 0.0

            # Check limits
            violations = []

            # Single position limit
            if proposed_exposure > self.max_single_position:
                violations.append(f"Single position limit exceeded: {proposed_exposure:.2%} > {self.max_single_position:.2%}")

            # Portfolio exposure limit
            new_total_exposure = current_exposure.get('total_exposure', 0) + proposed_exposure
            if new_total_exposure > self.max_portfolio_exposure:
                violations.append(f"Portfolio exposure limit exceeded: {new_total_exposure:.2%} > {self.max_portfolio_exposure:.2%}")

            return {
                'allowed': len(violations) == 0,
                'violations': violations,
                'proposed_exposure': proposed_exposure,
                'current_total_exposure': current_exposure.get('total_exposure', 0)
            }

        except Exception as e:
            self.logger.error(f"Error checking exposure limits: {e}")
            return {'allowed': False, 'violations': [f"Error: {e}"], 'proposed_exposure': 0.0}

    def get_max_position_size(self, symbol: str, current_price: float,
                            current_positions: Dict[str, Any]) -> float:
        """Get maximum allowed position size for a symbol"""
        try:
            current_exposure = self.calculate_portfolio_exposure(current_positions)
            total_value = current_exposure.get('total_value', 0)

            if total_value == 0:
                # If no current positions, use a conservative default
                return 1000.0  # Default max position value

            # Calculate max position value based on single position limit
            max_position_value = total_value * self.max_single_position

            # Convert to quantity
            if current_price > 0:
                max_quantity = max_position_value / current_price
                return max_quantity
            else:
                return 0.0

        except Exception as e:
            self.logger.error(f"Error calculating max position size: {e}")
            return 0.0

    def update_exposure_tracking(self, symbol: str, position_data: Dict[str, Any]):
        """Update exposure tracking for a position"""
        try:
            self.current_exposures[symbol] = {
                'value': position_data.get('value', 0),
                'quantity': position_data.get('quantity', 0),
                'last_updated': datetime.now(),
                'risk_metrics': self.risk_calculator.assess_position_risk(
                    position_data.get('quantity', 0),
                    position_data.get('entry_price', 0),
                    position_data.get('current_price', 0)
                )
            }

        except Exception as e:
            self.logger.error(f"Error updating exposure tracking: {e}")

    def get_exposure_summary(self) -> Dict[str, Any]:
        """Get summary of current exposures"""
        try:
            total_exposure = sum(exp.get('value', 0) for exp in self.current_exposures.values())

            return {
                'total_positions': len(self.current_exposures),
                'total_exposure_value': total_exposure,
                'positions': self.current_exposures,
                'limits': {
                    'max_portfolio_exposure': self.max_portfolio_exposure,
                    'max_single_position': self.max_single_position,
                    'max_sector_exposure': self.max_sector_exposure
                },
                'last_updated': datetime.now()
            }

        except Exception as e:
            self.logger.error(f"Error getting exposure summary: {e}")
            return {'total_positions': 0, 'total_exposure_value': 0.0}
