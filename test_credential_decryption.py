#!/usr/bin/env python3
"""
Test Credential Decryption
Tests that the credential decryption system works properly
"""

import os
import sys
from pathlib import Path

# Add project root to path
PROJECT_ROOT = Path(__file__).parent
sys.path.insert(0, str(PROJECT_ROOT))

def test_credential_decryption():
    """Test credential decryption functionality"""
    print("🔐 [TEST] Testing credential decryption...")
    
    try:
        # Load environment variables
        from dotenv import load_dotenv
        load_dotenv('.env')
        
        # Import decryption function - try HybridCrypto first
        try:
            from src.utils.cryptpography.hybrid import HybridCrypto
            crypto = HybridCrypto('src/utils/cryptography/private.pem')
            decrypt_value = crypto.decrypt_value
            print("✅ [TEST] Using HybridCrypto for decryption")
        except Exception as e:
            print(f"⚠️ [TEST] HybridCrypto failed, using fallback: {e}")
            from src.utils.cryptography.secure_credentials import decrypt_value
        
        # Test Coinbase credentials
        coinbase_api_key = os.getenv('ENCRYPTED_COINBASE_API_KEY_NAME')
        coinbase_private_key = os.getenv('ENCRYPTED_COINBASE_PRIVATE_KEY')
        
        if coinbase_api_key:
            print(f"🔐 [TEST] Original encrypted Coinbase API key: {coinbase_api_key[:50]}...")
            decrypted_key = decrypt_value(coinbase_api_key)
            print(f"🔓 [TEST] Decrypted Coinbase API key: {decrypted_key[:50]}...")
            
            # Check if it's still encrypted
            if decrypted_key.startswith('gAAAAA'):
                print("❌ [TEST] Decryption failed - still encrypted!")
                return False
            else:
                print("✅ [TEST] Coinbase API key successfully decrypted!")
        
        if coinbase_private_key:
            print(f"🔐 [TEST] Testing Coinbase private key decryption...")
            decrypted_private_key = decrypt_value(coinbase_private_key)
            
            # Check if it's properly decrypted (should start with -----BEGIN)
            if decrypted_private_key.startswith('-----BEGIN'):
                print("✅ [TEST] Coinbase private key successfully decrypted!")
            elif decrypted_private_key.startswith('gAAAAA'):
                print("❌ [TEST] Private key decryption failed - still encrypted!")
                return False
            else:
                print(f"🔓 [TEST] Decrypted private key format: {decrypted_private_key[:50]}...")
        
        # Test Bybit credentials
        bybit_api_key = os.getenv('ENCRYPTED_BYBIT_API_KEY')
        bybit_api_secret = os.getenv('ENCRYPTED_BYBIT_API_SECRET')
        
        if bybit_api_key:
            print(f"🔐 [TEST] Testing Bybit API key decryption...")
            decrypted_bybit_key = decrypt_value(bybit_api_key)
            
            if not decrypted_bybit_key.startswith('gAAAAA'):
                print("✅ [TEST] Bybit API key successfully decrypted!")
            else:
                print("❌ [TEST] Bybit API key decryption failed!")
                return False
        
        print("🎉 [TEST] All credential decryption tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ [TEST] Credential decryption test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_credential_decryption()
    sys.exit(0 if success else 1)
