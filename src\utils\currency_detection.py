"""
Professional-grade currency detection and management utilities
"""
import logging
from typing import Dict, List, Optional, Set, Tuple
from decimal import Decimal
import re

logger = logging.getLogger(__name__)

class CurrencyDetector:
    """Advanced currency detection and validation system"""
    
    def __init__(self):
        # Major cryptocurrencies
        self.major_cryptos = {
            'BTC', 'ETH', 'USDT', 'USDC', 'BNB', 'XRP', 'ADA', 'SOL', 'DOGE', 'DOT',
            'AVAX', 'SHIB', 'MATIC', 'LTC', 'UNI', 'LINK', 'ATOM', 'ETC', 'XLM', 'BCH'
        }
        
        # Stablecoins
        self.stablecoins = {
            'USDT', 'USDC', 'BUSD', 'DAI', 'TUSD', 'USDP', 'FRAX', 'LUSD'
        }
        
        # Fiat currencies
        self.fiat_currencies = {
            'USD', 'EUR', 'GBP', 'JPY', 'CNY', 'KRW', 'AUD', 'CAD', 'CHF', 'SEK'
        }
        
        # Common trading pairs patterns
        self.pair_patterns = [
            r'^([A-Z0-9]+)(USDT|USDC|BTC|ETH|BNB)$',  # Standard pairs
            r'^(1000000)([A-Z]+)(USDT)$',              # 1000000X pairs
            r'^(10000)([A-Z]+)(USDT)$',                # 10000X pairs
            r'^(1000)([A-Z]+)(USDT)$',                 # 1000X pairs
        ]
        
        logger.info("🔍 [CURRENCY-DETECTOR] Professional currency detection system initialized")
    
    def detect_base_quote(self, symbol: str) -> Tuple[str, str]:
        """Detect base and quote currencies from a trading symbol"""
        try:
            # Try pattern matching first
            for pattern in self.pair_patterns:
                match = re.match(pattern, symbol)
                if match:
                    groups = match.groups()
                    if len(groups) == 3:  # Multiplier + base + quote
                        multiplier, base, quote = groups
                        return f"{multiplier}{base}", quote
                    elif len(groups) == 2:  # Base + quote
                        base, quote = groups
                        return base, quote
            
            # Fallback: try common quote currencies
            for quote in ['USDT', 'USDC', 'BTC', 'ETH', 'BNB']:
                if symbol.endswith(quote):
                    base = symbol[:-len(quote)]
                    if base:
                        return base, quote
            
            # If no pattern matches, assume the last 3-4 chars are quote
            if len(symbol) > 6:
                return symbol[:-4], symbol[-4:]
            else:
                return symbol[:-3], symbol[-3:]
                
        except Exception as e:
            logger.error(f"Error detecting base/quote for {symbol}: {e}")
            return symbol, "USDT"  # Default fallback
    
    def is_stablecoin(self, currency: str) -> bool:
        """Check if a currency is a stablecoin"""
        return currency.upper() in self.stablecoins
    
    def is_major_crypto(self, currency: str) -> bool:
        """Check if a currency is a major cryptocurrency"""
        return currency.upper() in self.major_cryptos
    
    def is_fiat(self, currency: str) -> bool:
        """Check if a currency is fiat"""
        return currency.upper() in self.fiat_currencies
    
    def get_currency_type(self, currency: str) -> str:
        """Get the type of currency"""
        currency = currency.upper()
        
        if self.is_stablecoin(currency):
            return "stablecoin"
        elif self.is_major_crypto(currency):
            return "major_crypto"
        elif self.is_fiat(currency):
            return "fiat"
        else:
            return "altcoin"
    
    def validate_trading_pair(self, symbol: str) -> Dict[str, any]:
        """Validate and analyze a trading pair"""
        try:
            base, quote = self.detect_base_quote(symbol)
            
            return {
                'symbol': symbol,
                'base': base,
                'quote': quote,
                'base_type': self.get_currency_type(base),
                'quote_type': self.get_currency_type(quote),
                'is_valid': len(base) > 0 and len(quote) > 0,
                'is_crypto_pair': not (self.is_fiat(base) or self.is_fiat(quote)),
                'has_stablecoin': self.is_stablecoin(base) or self.is_stablecoin(quote),
                'risk_level': self._assess_risk_level(base, quote)
            }
            
        except Exception as e:
            logger.error(f"Error validating trading pair {symbol}: {e}")
            return {
                'symbol': symbol,
                'base': symbol,
                'quote': 'USDT',
                'is_valid': False,
                'error': str(e)
            }
    
    def _assess_risk_level(self, base: str, quote: str) -> str:
        """Assess risk level of a trading pair"""
        base_type = self.get_currency_type(base)
        quote_type = self.get_currency_type(quote)
        
        # Stablecoin pairs are lowest risk
        if base_type == "stablecoin" or quote_type == "stablecoin":
            if base_type == "major_crypto" or quote_type == "major_crypto":
                return "low"
            else:
                return "medium"
        
        # Major crypto pairs
        if base_type == "major_crypto" and quote_type == "major_crypto":
            return "medium"
        
        # Altcoin pairs are higher risk
        if base_type == "altcoin" or quote_type == "altcoin":
            return "high"
        
        return "medium"
    
    def get_supported_quote_currencies(self) -> List[str]:
        """Get list of supported quote currencies"""
        return list(self.stablecoins.union(self.major_cryptos))
    
    def normalize_symbol(self, symbol: str) -> str:
        """Normalize a trading symbol to standard format"""
        try:
            base, quote = self.detect_base_quote(symbol)
            return f"{base}{quote}".upper()
        except Exception:
            return symbol.upper()

# Global currency detector instance
currency_detector = CurrencyDetector()

# Export main functions
def detect_base_quote(symbol: str) -> Tuple[str, str]:
    """Convenience function to detect base and quote currencies"""
    return currency_detector.detect_base_quote(symbol)

def validate_trading_pair(symbol: str) -> Dict[str, any]:
    """Convenience function to validate trading pair"""
    return currency_detector.validate_trading_pair(symbol)

def is_stablecoin(currency: str) -> bool:
    """Convenience function to check if currency is stablecoin"""
    return currency_detector.is_stablecoin(currency)
