"""
Internet Crawling Agent for Real-Time Trading Data
Crawls the internet for news, social sentiment, regulatory announcements, and market events
"""

import asyncio
import aiohttp
import logging
import json
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass, asdict
from urllib.parse import urljoin, urlparse
try:
    import feedparser
    FEEDPARSER_AVAILABLE = True
except ImportError:
    FEEDPARSER_AVAILABLE = False
    feedparser = None

try:
    from bs4 import BeautifulSoup
    BS4_AVAILABLE = True
except ImportError:
    BS4_AVAILABLE = False
    BeautifulSoup = None
import sqlite3
from pathlib import Path

logger = logging.getLogger(__name__)

@dataclass
class NewsItem:
    """Data structure for news items"""
    id: str
    title: str
    content: str
    source: str
    url: str
    timestamp: datetime
    sentiment_score: float = 0.0
    relevance_score: float = 0.0
    keywords: List[str] = None
    category: str = "general"
    impact_level: str = "low"  # low, medium, high, critical
    
    def __post_init__(self):
        if self.keywords is None:
            self.keywords = []

@dataclass
class MarketEvent:
    """Data structure for market events"""
    event_id: str
    event_type: str  # regulatory, earnings, partnership, hack, etc.
    title: str
    description: str
    source: str
    timestamp: datetime
    affected_symbols: List[str] = None
    impact_score: float = 0.0
    confidence: float = 0.0
    
    def __post_init__(self):
        if self.affected_symbols is None:
            self.affected_symbols = []

class InternetCrawler:
    """
    Internet Crawling Agent for Real-Time Trading Data
    Crawls multiple sources for trading-relevant information
    """
    
    def __init__(self, db_path: str = "data/internet_data.db"):
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # News sources and RSS feeds
        self.news_sources = {
            'coindesk': 'https://www.coindesk.com/arc/outboundfeeds/rss/',
            'cointelegraph': 'https://cointelegraph.com/rss',
            'decrypt': 'https://decrypt.co/feed',
            'theblock': 'https://www.theblock.co/rss.xml',
            'bitcoinmagazine': 'https://bitcoinmagazine.com/.rss/full/',
            'cryptonews': 'https://cryptonews.com/news/feed'
        }
        
        # Social media and sentiment sources
        self.social_sources = {
            'reddit_crypto': 'https://www.reddit.com/r/CryptoCurrency/.rss',
            'reddit_bitcoin': 'https://www.reddit.com/r/Bitcoin/.rss',
            'reddit_ethereum': 'https://www.reddit.com/r/ethereum/.rss'
        }
        
        # Regulatory and official sources
        self.regulatory_sources = {
            'sec_releases': 'https://www.sec.gov/news/pressreleases.rss',
            'cftc_releases': 'https://www.cftc.gov/PressRoom/PressReleases/rss.xml'
        }
        
        # Keywords for relevance scoring
        self.crypto_keywords = {
            'bitcoin', 'btc', 'ethereum', 'eth', 'crypto', 'cryptocurrency',
            'blockchain', 'defi', 'nft', 'altcoin', 'trading', 'exchange',
            'regulation', 'sec', 'cftc', 'fed', 'central bank', 'cbdc',
            'stablecoin', 'usdt', 'usdc', 'binance', 'coinbase', 'ftx'
        }
        
        self.high_impact_keywords = {
            'regulation', 'ban', 'approval', 'etf', 'sec', 'lawsuit',
            'hack', 'exploit', 'crash', 'surge', 'partnership', 'adoption'
        }
        
        # Initialize database and session
        self._init_database()
        self.session = None
        self.crawl_interval = 60  # 1 minute for faster updates
        self.max_articles_per_source = 100  # More articles for better coverage

        # Enhanced real-time processing
        self.priority_keywords = {
            'breaking', 'urgent', 'alert', 'emergency', 'crash', 'surge',
            'halt', 'suspend', 'approve', 'reject', 'launch', 'partnership'
        }

        # Cache for fast access
        self.sentiment_cache = {}
        self.last_cache_update = datetime.now()
        self.cache_ttl = 300  # 5 minutes cache TTL

        # Performance metrics
        self.crawl_stats = {
            'total_crawls': 0,
            'successful_crawls': 0,
            'items_processed': 0,
            'avg_processing_time': 0.0
        }

        logger.info("Enhanced InternetCrawler initialized - ready for real-time trading data")

    async def safe_start_crawling(self):
        """Safely start crawling with graceful error handling"""
        try:
            await self.start_crawling()
        except Exception as e:
            logger.error(f"Internet crawler failed to start, continuing without it: {e}")
            # Don't re-raise the exception to prevent breaking the main system

    def get_fallback_sentiment(self) -> Dict[str, Any]:
        """Get fallback sentiment data when crawler is unavailable"""
        return {
            'overall': 0.0,
            'confidence': 0.1,
            'news_count': 0,
            'avg_sentiment': 0.0,
            'critical_news_count': 0,
            'has_breaking_news': False,
            'critical_sentiment': 0.0,
            'source': 'fallback',
            'status': 'crawler_unavailable'
        }
    
    def _init_database(self):
        """Initialize SQLite database for crawled data"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS news_items (
                    id TEXT PRIMARY KEY,
                    title TEXT NOT NULL,
                    content TEXT,
                    source TEXT NOT NULL,
                    url TEXT NOT NULL,
                    timestamp TEXT NOT NULL,
                    sentiment_score REAL DEFAULT 0,
                    relevance_score REAL DEFAULT 0,
                    keywords TEXT,
                    category TEXT DEFAULT 'general',
                    impact_level TEXT DEFAULT 'low',
                    processed INTEGER DEFAULT 0
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS market_events (
                    event_id TEXT PRIMARY KEY,
                    event_type TEXT NOT NULL,
                    title TEXT NOT NULL,
                    description TEXT,
                    source TEXT NOT NULL,
                    timestamp TEXT NOT NULL,
                    affected_symbols TEXT,
                    impact_score REAL DEFAULT 0,
                    confidence REAL DEFAULT 0,
                    processed INTEGER DEFAULT 0
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS crawl_status (
                    source TEXT PRIMARY KEY,
                    last_crawl TEXT,
                    last_success TEXT,
                    error_count INTEGER DEFAULT 0,
                    total_items INTEGER DEFAULT 0
                )
            """)
            
            conn.commit()
    
    async def start_crawling(self):
        """Start the enhanced continuous crawling process"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=15),  # Faster timeout
            headers={'User-Agent': 'TradingBot/1.0 (Educational Purpose)'},
            connector=aiohttp.TCPConnector(limit=20, limit_per_host=5)  # Connection pooling
        )

        logger.info("Starting enhanced internet crawling for real-time trading data")

        while True:
            try:
                start_time = datetime.now()
                await self._crawl_all_sources()

                # Update performance metrics
                processing_time = (datetime.now() - start_time).total_seconds()
                self.crawl_stats['total_crawls'] += 1
                self.crawl_stats['successful_crawls'] += 1
                self.crawl_stats['avg_processing_time'] = (
                    (self.crawl_stats['avg_processing_time'] * (self.crawl_stats['total_crawls'] - 1) + processing_time) /
                    self.crawl_stats['total_crawls']
                )

                logger.debug(f"Crawl cycle completed in {processing_time:.2f}s")

                # Adaptive sleep based on processing time
                sleep_time = max(self.crawl_interval - processing_time, 10)  # Minimum 10s sleep
                await asyncio.sleep(sleep_time)

            except Exception as e:
                logger.error(f"Error in crawling loop: {e}")
                await asyncio.sleep(30)  # Shorter wait on error for faster recovery
    
    async def _crawl_all_sources(self):
        """Crawl all configured sources"""
        tasks = []
        
        # Crawl news sources
        for source_name, url in self.news_sources.items():
            tasks.append(self._crawl_rss_feed(source_name, url, 'news'))
        
        # Crawl social sources
        for source_name, url in self.social_sources.items():
            tasks.append(self._crawl_rss_feed(source_name, url, 'social'))
        
        # Crawl regulatory sources
        for source_name, url in self.regulatory_sources.items():
            tasks.append(self._crawl_rss_feed(source_name, url, 'regulatory'))
        
        # Execute all crawling tasks
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Log results
        successful = sum(1 for r in results if not isinstance(r, Exception))
        logger.info(f"Crawling completed: {successful}/{len(tasks)} sources successful")
    
    async def _crawl_rss_feed(self, source_name: str, url: str, category: str):
        """Crawl a single RSS feed - GRACEFUL FALLBACK"""
        try:
            # Add timeout and graceful handling
            async with self.session.get(url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                if response.status != 200:
                    logger.warning(f"Failed to fetch {source_name}: HTTP {response.status}")
                    await self._update_crawl_status(source_name, 0, error=True)
                    return

                content = await response.text()

                # Graceful feedparser handling
                try:
                    if not FEEDPARSER_AVAILABLE:
                        logger.warning(f"Feedparser not available, skipping RSS parsing for {source_name}")
                        await self._update_crawl_status(source_name, 0)
                        return

                    feed = feedparser.parse(content)
                    if not hasattr(feed, 'entries') or not feed.entries:
                        logger.warning(f"No entries found in {source_name} feed")
                        await self._update_crawl_status(source_name, 0)
                        return
                except Exception as parse_error:
                    logger.warning(f"Failed to parse {source_name} feed: {parse_error}")
                    await self._update_crawl_status(source_name, 0, error=True)
                    return
                
                new_items = 0
                for entry in feed.entries[:self.max_articles_per_source]:
                    try:
                        # Create news item
                        item_id = self._generate_item_id(entry.link, entry.title)
                        
                        # Check if already processed
                        if await self._is_item_processed(item_id):
                            continue
                        
                        # Extract timestamp
                        timestamp = self._parse_timestamp(entry)
                        
                        # Skip old items (older than 24 hours)
                        if timestamp < datetime.now() - timedelta(hours=24):
                            continue
                        
                        # Create news item
                        news_item = NewsItem(
                            id=item_id,
                            title=entry.title,
                            content=entry.get('summary', ''),
                            source=source_name,
                            url=entry.link,
                            timestamp=timestamp,
                            category=category
                        )
                        
                        # Analyze content
                        await self._analyze_news_item(news_item)
                        
                        # Store in database
                        await self._store_news_item(news_item)
                        
                        new_items += 1
                        
                    except Exception as e:
                        logger.error(f"Error processing entry from {source_name}: {e}")
                
                # Update crawl status
                await self._update_crawl_status(source_name, new_items)
                
                if new_items > 0:
                    logger.info(f"Crawled {new_items} new items from {source_name}")
                
        except Exception as e:
            logger.error(f"Error crawling {source_name}: {e}")
            await self._update_crawl_status(source_name, 0, error=True)
    
    def _generate_item_id(self, url: str, title: str) -> str:
        """Generate unique ID for news item"""
        import hashlib
        content = f"{url}_{title}"
        return hashlib.md5(content.encode()).hexdigest()
    
    def _parse_timestamp(self, entry) -> datetime:
        """Parse timestamp from RSS entry"""
        try:
            if hasattr(entry, 'published_parsed') and entry.published_parsed:
                import time
                return datetime.fromtimestamp(time.mktime(entry.published_parsed))
            elif hasattr(entry, 'updated_parsed') and entry.updated_parsed:
                import time
                return datetime.fromtimestamp(time.mktime(entry.updated_parsed))
            else:
                return datetime.now()
        except:
            return datetime.now()
    
    async def _analyze_news_item(self, news_item: NewsItem):
        """Enhanced analysis of news item for sentiment and relevance"""
        try:
            text = f"{news_item.title} {news_item.content}".lower()

            # Enhanced relevance scoring with weighted keywords
            keyword_weights = {
                'bitcoin': 3, 'btc': 3, 'ethereum': 3, 'eth': 3,
                'regulation': 4, 'sec': 4, 'etf': 5, 'approval': 4,
                'hack': 5, 'exploit': 5, 'crash': 4, 'surge': 4,
                'partnership': 3, 'adoption': 3, 'ban': 5
            }

            relevance_score = 0.0
            for keyword, weight in keyword_weights.items():
                if keyword in text:
                    relevance_score += weight

            # Add base score for general crypto keywords
            general_matches = sum(1 for keyword in self.crypto_keywords if keyword in text)
            relevance_score += general_matches * 0.5

            news_item.relevance_score = min(relevance_score / 20.0, 1.0)  # Normalize to 0-1

            # Enhanced impact level calculation
            priority_matches = sum(1 for keyword in self.priority_keywords if keyword in text)
            high_impact_matches = sum(1 for keyword in self.high_impact_keywords if keyword in text)

            if priority_matches >= 1 or high_impact_matches >= 3:
                news_item.impact_level = "critical"
            elif high_impact_matches >= 2:
                news_item.impact_level = "high"
            elif high_impact_matches >= 1:
                news_item.impact_level = "medium"
            else:
                news_item.impact_level = "low"

            # Extract all relevant keywords
            found_keywords = []
            for keyword in self.crypto_keywords:
                if keyword in text:
                    found_keywords.append(keyword)
            for keyword in self.high_impact_keywords:
                if keyword in text and keyword not in found_keywords:
                    found_keywords.append(keyword)
            news_item.keywords = found_keywords

            # Enhanced sentiment analysis
            positive_words = [
                'surge', 'gain', 'bull', 'bullish', 'positive', 'growth', 'adoption',
                'partnership', 'approve', 'approval', 'launch', 'success', 'breakthrough',
                'rally', 'moon', 'pump', 'green', 'profit', 'win', 'victory'
            ]
            negative_words = [
                'crash', 'drop', 'bear', 'bearish', 'negative', 'ban', 'hack', 'exploit',
                'decline', 'fall', 'dump', 'red', 'loss', 'fail', 'reject', 'concern',
                'warning', 'risk', 'threat', 'volatile', 'uncertainty'
            ]

            # Weight sentiment by word importance
            positive_score = 0.0
            negative_score = 0.0

            for word in positive_words:
                if word in text:
                    weight = 2.0 if word in ['approval', 'launch', 'partnership'] else 1.0
                    positive_score += weight

            for word in negative_words:
                if word in text:
                    weight = 2.0 if word in ['ban', 'hack', 'crash'] else 1.0
                    negative_score += weight

            if positive_score + negative_score > 0:
                news_item.sentiment_score = (positive_score - negative_score) / (positive_score + negative_score)
            else:
                news_item.sentiment_score = 0.0

            # Boost sentiment for high-impact news
            if news_item.impact_level in ['high', 'critical']:
                news_item.sentiment_score *= 1.5
                news_item.sentiment_score = max(-1.0, min(1.0, news_item.sentiment_score))

        except Exception as e:
            logger.error(f"Error analyzing news item: {e}")

    async def get_real_time_sentiment(self) -> Dict[str, Any]:
        """Get real-time market sentiment with caching for performance - GRACEFUL FALLBACK"""
        try:
            # Check cache first
            now = datetime.now()
            if (self.sentiment_cache and
                (now - self.last_cache_update).total_seconds() < self.cache_ttl):
                return self.sentiment_cache

            # Calculate fresh sentiment with graceful fallback
            try:
                sentiment_data = await self.get_market_sentiment()
            except Exception as sentiment_error:
                logger.warning(f"Failed to get market sentiment, using neutral fallback: {sentiment_error}")
                sentiment_data = {
                    'overall': 0.0,
                    'confidence': 0.3,
                    'news_count': 0,
                    'avg_sentiment': 0.0,
                    'source': 'fallback'
                }

            # Add real-time enhancements with graceful fallback
            try:
                recent_critical = await self._get_critical_news(hours=1)
                if recent_critical:
                    sentiment_data['critical_news_count'] = len(recent_critical)
                    sentiment_data['has_breaking_news'] = True

                    # Adjust sentiment based on critical news
                    critical_sentiment = sum(item.sentiment_score for item in recent_critical) / len(recent_critical)
                    sentiment_data['critical_sentiment'] = critical_sentiment

                    # Boost overall sentiment if critical news is very positive/negative
                    if abs(critical_sentiment) > 0.7:
                        sentiment_data['overall'] = (sentiment_data['overall'] + critical_sentiment) / 2
                else:
                    sentiment_data['critical_news_count'] = 0
                    sentiment_data['has_breaking_news'] = False
                    sentiment_data['critical_sentiment'] = 0.0
            except Exception as critical_error:
                logger.warning(f"Failed to get critical news, using defaults: {critical_error}")
                sentiment_data['critical_news_count'] = 0
                sentiment_data['has_breaking_news'] = False
                sentiment_data['critical_sentiment'] = 0.0

            # Update cache
            self.sentiment_cache = sentiment_data
            self.last_cache_update = now

            return sentiment_data

        except Exception as e:
            logger.error(f"Error getting real-time sentiment, using emergency fallback: {e}")
            # CRITICAL: Return neutral sentiment to prevent system failure
            return {
                'overall': 0.0,
                'confidence': 0.1,
                'news_count': 0,
                'avg_sentiment': 0.0,
                'critical_news_count': 0,
                'has_breaking_news': False,
                'critical_sentiment': 0.0,
                'source': 'emergency_fallback',
                'error': str(e)
            }

    async def _get_critical_news(self, hours: int = 1) -> List[NewsItem]:
        """Get critical/breaking news from recent hours"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT * FROM news_items
                    WHERE timestamp > ? AND impact_level IN ('critical', 'high')
                    ORDER BY timestamp DESC, relevance_score DESC
                    LIMIT 20
                """, (cutoff_time.isoformat(),))

                news_items = []
                for row in cursor.fetchall():
                    news_item = NewsItem(
                        id=row[0],
                        title=row[1],
                        content=row[2],
                        source=row[3],
                        url=row[4],
                        timestamp=datetime.fromisoformat(row[5]),
                        sentiment_score=row[6],
                        relevance_score=row[7],
                        keywords=json.loads(row[8]) if row[8] else [],
                        category=row[9],
                        impact_level=row[10]
                    )
                    news_items.append(news_item)

                return news_items

        except Exception as e:
            logger.error(f"Error getting critical news: {e}")
            return []
    
    async def _is_item_processed(self, item_id: str) -> bool:
        """Check if item has already been processed"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("SELECT 1 FROM news_items WHERE id = ?", (item_id,))
                return cursor.fetchone() is not None
        except:
            return False
    
    async def _store_news_item(self, news_item: NewsItem):
        """Store news item in database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO news_items 
                    (id, title, content, source, url, timestamp, sentiment_score, 
                     relevance_score, keywords, category, impact_level)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    news_item.id,
                    news_item.title,
                    news_item.content,
                    news_item.source,
                    news_item.url,
                    news_item.timestamp.isoformat(),
                    news_item.sentiment_score,
                    news_item.relevance_score,
                    json.dumps(news_item.keywords),
                    news_item.category,
                    news_item.impact_level
                ))
                conn.commit()
        except Exception as e:
            logger.error(f"Error storing news item: {e}")
    
    async def _update_crawl_status(self, source: str, items_count: int, error: bool = False):
        """Update crawl status for a source"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                now = datetime.now().isoformat()
                
                if error:
                    conn.execute("""
                        INSERT OR REPLACE INTO crawl_status 
                        (source, last_crawl, error_count, total_items)
                        VALUES (?, ?, COALESCE((SELECT error_count FROM crawl_status WHERE source = ?) + 1, 1), 
                                COALESCE((SELECT total_items FROM crawl_status WHERE source = ?), 0))
                    """, (source, now, source, source))
                else:
                    conn.execute("""
                        INSERT OR REPLACE INTO crawl_status 
                        (source, last_crawl, last_success, error_count, total_items)
                        VALUES (?, ?, ?, 0, COALESCE((SELECT total_items FROM crawl_status WHERE source = ?), 0) + ?)
                    """, (source, now, now, source, items_count))
                
                conn.commit()
        except Exception as e:
            logger.error(f"Error updating crawl status: {e}")

    async def get_recent_news(self, hours: int = 24, min_relevance: float = 0.3) -> List[NewsItem]:
        """Get recent news items with minimum relevance"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT * FROM news_items
                    WHERE timestamp > ? AND relevance_score >= ?
                    ORDER BY timestamp DESC, relevance_score DESC
                    LIMIT 100
                """, (cutoff_time.isoformat(), min_relevance))

                news_items = []
                for row in cursor.fetchall():
                    news_item = NewsItem(
                        id=row[0],
                        title=row[1],
                        content=row[2],
                        source=row[3],
                        url=row[4],
                        timestamp=datetime.fromisoformat(row[5]),
                        sentiment_score=row[6],
                        relevance_score=row[7],
                        keywords=json.loads(row[8]) if row[8] else [],
                        category=row[9],
                        impact_level=row[10]
                    )
                    news_items.append(news_item)

                return news_items

        except Exception as e:
            logger.error(f"Error getting recent news: {e}")
            return []

    async def get_market_sentiment(self) -> Dict[str, float]:
        """Calculate overall market sentiment from recent news"""
        try:
            recent_news = await self.get_recent_news(hours=6, min_relevance=0.4)

            if not recent_news:
                return {'overall': 0.0, 'confidence': 0.0}

            # Weight sentiment by relevance and impact
            weighted_sentiment = 0.0
            total_weight = 0.0

            for item in recent_news:
                weight = item.relevance_score
                if item.impact_level == "high":
                    weight *= 2.0
                elif item.impact_level == "medium":
                    weight *= 1.5

                weighted_sentiment += item.sentiment_score * weight
                total_weight += weight

            overall_sentiment = weighted_sentiment / total_weight if total_weight > 0 else 0.0
            confidence = min(len(recent_news) / 20.0, 1.0)  # More news = higher confidence

            return {
                'overall': overall_sentiment,
                'confidence': confidence,
                'news_count': len(recent_news),
                'high_impact_count': sum(1 for item in recent_news if item.impact_level == "high")
            }

        except Exception as e:
            logger.error(f"Error calculating market sentiment: {e}")
            return {'overall': 0.0, 'confidence': 0.0}

    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get crawler performance metrics"""
        return {
            **self.crawl_stats,
            'cache_hit_rate': 1.0 if self.sentiment_cache else 0.0,
            'last_cache_update': self.last_cache_update.isoformat() if self.last_cache_update else None,
            'crawl_interval': self.crawl_interval,
            'sources_configured': len(self.news_sources) + len(self.social_sources) + len(self.regulatory_sources)
        }

    async def get_fast_market_overview(self) -> Dict[str, Any]:
        """Get fast market overview for real-time trading decisions"""
        try:
            # Use cached sentiment if available
            sentiment_data = await self.get_real_time_sentiment()

            # Get recent high-impact news count
            recent_news = await self.get_recent_news(hours=2, min_relevance=0.6)
            high_impact_news = [item for item in recent_news if item.impact_level in ['high', 'critical']]

            # Calculate market volatility indicator based on news
            volatility_indicator = min(len(high_impact_news) / 5.0, 1.0)  # Normalize to 0-1

            # Calculate news momentum (recent vs older news sentiment)
            recent_sentiment = 0.0
            older_sentiment = 0.0

            for item in recent_news:
                age_hours = (datetime.now() - item.timestamp).total_seconds() / 3600
                if age_hours <= 1:
                    recent_sentiment += item.sentiment_score
                elif age_hours <= 6:
                    older_sentiment += item.sentiment_score

            momentum = recent_sentiment - older_sentiment if recent_sentiment != 0 or older_sentiment != 0 else 0.0

            return {
                'sentiment': sentiment_data.get('overall', 0.0),
                'confidence': sentiment_data.get('confidence', 0.0),
                'volatility_indicator': volatility_indicator,
                'news_momentum': momentum,
                'breaking_news': sentiment_data.get('has_breaking_news', False),
                'high_impact_count': len(high_impact_news),
                'total_news_count': len(recent_news),
                'last_updated': datetime.now().isoformat(),
                'processing_time_ms': self.crawl_stats['avg_processing_time'] * 1000
            }

        except Exception as e:
            logger.error(f"Error getting fast market overview: {e}")
            return {
                'sentiment': 0.0,
                'confidence': 0.0,
                'volatility_indicator': 0.5,
                'news_momentum': 0.0,
                'breaking_news': False,
                'error': str(e)
            }

    async def close(self):
        """Close the crawler and cleanup resources"""
        if self.session:
            await self.session.close()
        logger.info("Internet crawler closed")

# Global instance for easy access
_internet_crawler = None

async def get_internet_crawler() -> InternetCrawler:
    """Get or create global internet crawler instance"""
    global _internet_crawler
    if _internet_crawler is None:
        _internet_crawler = InternetCrawler()
    return _internet_crawler

async def start_internet_crawling():
    """Start the internet crawling service"""
    crawler = await get_internet_crawler()
    await crawler.start_crawling()

async def get_fast_trading_data() -> Dict[str, Any]:
    """Get fast trading data for immediate use"""
    crawler = await get_internet_crawler()
    return await crawler.get_fast_market_overview()
