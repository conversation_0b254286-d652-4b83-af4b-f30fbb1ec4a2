"""
Cryptography utilities for secure credential management
"""

from .secure_credentials import (
    encrypt_value,
    decrypt_value,
    get_secure_credential,
    load_encrypted_credential,
    save_encrypted_credential,
    SecureCredentials
)

# Create hybrid module fallback
try:
    from ..cryptpography.hybrid import HybridCredentialDecryptor
except ImportError:
    # Fallback hybrid decryptor
    class HybridCredentialDecryptor:
        def __init__(self):
            self.available = False

        def decrypt_credentials(self, encrypted_data):
            raise NotImplementedError("Hybrid decryptor not available")

__all__ = [
    'encrypt_value',
    'decrypt_value',
    'get_secure_credential',
    'load_encrypted_credential',
    'save_encrypted_credential',
    'SecureCredentials',
    'HybridCredentialDecryptor'
]
