#!/usr/bin/env python3
"""
Comprehensive System Activation Test
Tests that ALL components are properly activated in main.py
"""

import asyncio
import logging
import sys
import os
import traceback
from pathlib import Path

# Add project root to path
PROJECT_ROOT = Path(__file__).parent
sys.path.insert(0, str(PROJECT_ROOT))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ComprehensiveSystemTest:
    """Test ALL components are activated and working"""
    
    def __init__(self):
        self.test_results = {}
        self.total_tests = 0
        self.passed_tests = 0
        self.failed_tests = 0
        
    async def run_all_tests(self):
        """Run comprehensive system tests"""
        logger.info("🚀 [TEST] Starting Comprehensive System Activation Tests...")
        
        # Test 1: Import Tests
        await self.test_imports()
        
        # Test 2: Component Initialization Tests
        await self.test_component_initialization()
        
        # Test 3: System Integration Tests
        await self.test_system_integration()
        
        # Test 4: Performance Tests
        await self.test_performance_targets()
        
        # Generate final report
        self.generate_test_report()
        
        return self.passed_tests == self.total_tests
    
    async def test_imports(self):
        """Test ALL critical imports work"""
        logger.info("📦 [TEST] Testing ALL critical imports...")
        
        import_tests = [
            # Neural Components
            ("src.neural", "RLAgentManager"),
            ("src.neural", "HybridTradingAgent"),
            ("src.neural", "PricePredictor"),
            ("src.neural", "AdvancedLSTMProcessor"),
            ("src.neural", "TransformerTradingModel"),
            ("src.neural", "MarketGraphNeuralNetwork"),
            ("src.neural", "EnhancedRiskPredictor"),
            ("src.neural", "EnhancedProfitPredictor"),
            
            # Trading Engines
            ("src.trading.futures_basis_trading_engine", "FuturesBasisTradingEngine"),
            ("src.trading.grid_trading_ml_engine", "GridTradingMLEngine"),
            ("src.trading.ai_market_making_engine", "AIMarketMakingEngine"),
            ("src.trading.volatility_options_engine", "VolatilityOptionsEngine"),
            ("src.trading.yield_optimization_engine", "YieldOptimizationEngine"),
            ("src.trading.time_optimization_engine", "GlobalTimeEfficiencyOptimizer"),
            ("src.trading.unified_trading_system", "UnifiedTradingSystem"),
            ("src.trading.multi_currency_trading_engine", "MultiCurrencyTradingEngine"),
            
            # Exchange Clients
            ("src.exchanges.bybit_client_fixed", "BybitClientFixed"),
            ("src.exchanges.coinbase_enhanced_client", "CoinbaseEnhancedClient"),
            ("src.exchanges.unified_exchange_adapters", "UnifiedExchangeAdapters"),
            
            # Data Feeds
            ("src.data_feeds.live_data_fetcher", "LiveDataFetcher"),
            ("src.data_feeds.real_time_data_aggregator", "RealTimeDataAggregator"),
            ("src.data_feeds.websocket_manager", "WebSocketManager"),
            
            # Optimization Systems
            ("src.optimization.trading_efficiency_optimizer", "TradingEfficiencyOptimizer"),
            ("src.performance.speed_optimizer", "SpeedOptimizer"),
            ("src.performance.performance_integration", "PerformanceIntegrator"),
            
            # Monitoring Systems
            ("src.monitoring.enhanced_trading_monitor", "EnhancedTradingMonitor"),
            ("src.monitoring.system_monitor", "SystemMonitor"),
            ("src.monitoring.performance_tracker", "PerformanceTracker"),
            
            # Learning Systems
            ("src.neural.persistent_learning_system", "PersistentLearningSystem"),
            ("src.neural.enterprise_memory_system", "EnterpriseNeuralMemorySystem"),
            ("src.learning.adaptive_learning_framework", "AdaptiveLearningFramework"),
        ]
        
        for module_name, class_name in import_tests:
            self.total_tests += 1
            try:
                module = __import__(module_name, fromlist=[class_name])
                component_class = getattr(module, class_name)
                self.test_results[f"import_{class_name}"] = "PASS"
                self.passed_tests += 1
                logger.info(f"✅ [IMPORT] {class_name}: PASS")
            except Exception as e:
                self.test_results[f"import_{class_name}"] = f"FAIL: {e}"
                self.failed_tests += 1
                logger.error(f"❌ [IMPORT] {class_name}: FAIL - {e}")
    
    async def test_component_initialization(self):
        """Test component initialization"""
        logger.info("🔧 [TEST] Testing component initialization...")
        
        self.total_tests += 1
        try:
            # Test main system initialization
            from main import ComprehensiveLiveTradingSystem
            
            trading_system = ComprehensiveLiveTradingSystem()
            
            # Check if all component dictionaries are initialized
            required_attributes = [
                'neural_components', 'trading_engines', 'exchange_clients',
                'data_feeds', 'optimization_engines', 'learning_systems',
                'monitoring_systems', 'risk_managers', 'performance_trackers'
            ]
            
            for attr in required_attributes:
                if not hasattr(trading_system, attr):
                    raise AttributeError(f"Missing required attribute: {attr}")
            
            self.test_results["component_initialization"] = "PASS"
            self.passed_tests += 1
            logger.info("✅ [INIT] Component initialization: PASS")
            
        except Exception as e:
            self.test_results["component_initialization"] = f"FAIL: {e}"
            self.failed_tests += 1
            logger.error(f"❌ [INIT] Component initialization: FAIL - {e}")
    
    async def test_system_integration(self):
        """Test system integration"""
        logger.info("🔗 [TEST] Testing system integration...")

        self.total_tests += 1
        try:
            # Test that main function exists and is callable
            import main

            if not callable(main.main):
                raise TypeError("main function is not callable")

            # Test that ComprehensiveLiveTradingSystem exists
            if not hasattr(main, 'ComprehensiveLiveTradingSystem'):
                raise AttributeError("ComprehensiveLiveTradingSystem not found in main")

            self.test_results["system_integration"] = "PASS"
            self.passed_tests += 1
            logger.info("✅ [INTEGRATION] System integration: PASS")

        except Exception as e:
            self.test_results["system_integration"] = f"FAIL: {e}"
            self.failed_tests += 1
            logger.error(f"❌ [INTEGRATION] System integration: FAIL - {e}")
    
    async def test_performance_targets(self):
        """Test performance targets are defined"""
        logger.info("🚀 [TEST] Testing performance targets...")
        
        performance_tests = [
            ("Speed Optimizer", "src.performance.speed_optimizer"),
            ("Performance Integrator", "src.performance.performance_integration"),
            ("Trading Efficiency Optimizer", "src.optimization.trading_efficiency_optimizer"),
        ]
        
        for test_name, module_name in performance_tests:
            self.total_tests += 1
            try:
                module = __import__(module_name, fromlist=[''])
                self.test_results[f"performance_{test_name}"] = "PASS"
                self.passed_tests += 1
                logger.info(f"✅ [PERFORMANCE] {test_name}: PASS")
            except Exception as e:
                self.test_results[f"performance_{test_name}"] = f"FAIL: {e}"
                self.failed_tests += 1
                logger.error(f"❌ [PERFORMANCE] {test_name}: FAIL - {e}")
    
    def generate_test_report(self):
        """Generate comprehensive test report"""
        logger.info("📊 [REPORT] Generating comprehensive test report...")
        
        success_rate = (self.passed_tests / self.total_tests * 100) if self.total_tests > 0 else 0
        
        print("\n" + "="*80)
        print("🎯 COMPREHENSIVE SYSTEM ACTIVATION TEST REPORT")
        print("="*80)
        print(f"📊 Total Tests: {self.total_tests}")
        print(f"✅ Passed: {self.passed_tests}")
        print(f"❌ Failed: {self.failed_tests}")
        print(f"📈 Success Rate: {success_rate:.1f}%")
        print("="*80)
        
        if self.failed_tests > 0:
            print("\n❌ FAILED TESTS:")
            for test_name, result in self.test_results.items():
                if result.startswith("FAIL"):
                    print(f"  • {test_name}: {result}")
        
        if success_rate >= 90:
            print("\n🎉 EXCELLENT: System activation is comprehensive!")
        elif success_rate >= 75:
            print("\n✅ GOOD: Most components are activated successfully!")
        elif success_rate >= 50:
            print("\n⚠️ WARNING: Some critical components may be missing!")
        else:
            print("\n❌ CRITICAL: Major system components are not activated!")
        
        print("="*80)

async def main():
    """Run comprehensive system tests"""
    try:
        test_system = ComprehensiveSystemTest()
        success = await test_system.run_all_tests()
        
        if success:
            logger.info("🎉 [SUCCESS] ALL tests passed - System is comprehensively activated!")
            return 0
        else:
            logger.error("❌ [FAILURE] Some tests failed - System activation incomplete!")
            return 1
            
    except Exception as e:
        logger.error(f"❌ [CRITICAL] Test execution failed: {e}")
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
