#!/usr/bin/env python3
"""
Unified Exchange Adapters System
Provides standardized interface for multiple exchanges with error handling, circuit breakers, and rate limiting
"""

import asyncio
import aiohttp
import hmac
import hashlib
import time
import logging
import os
from abc import ABC, abstractmethod
from urllib.parse import urlencode
from typing import Dict, Any, List, Optional, Coroutine
from decimal import Decimal

# Configure logging
logger = logging.getLogger(__name__)

# ----------------------------------------------------------------------------
# Error handling: solver, circuit breaker, rate limiter
# ----------------------------------------------------------------------------
class ErrorSolver:
    """Auto-resolve common API errors via parameter adjustment."""
    def __init__(self):
        self.adjustments: Dict[str, Dict[str, Any]] = {}

    async def solve(self, name: str, exc: Exception) -> bool:
        msg = str(exc).lower()
        if 'timeout' in msg:
            self.adjustments[name] = {'timeout': 30}
            logger.info(f"🔧 [{name}] Increased timeout to 30s")
            return True
        if '429' in msg or 'rate limit' in msg:
            self.adjustments[name] = {'backoff': 5}
            logger.info(f"🔧 [{name}] Set backoff to 5s")
            return True
        if 'insufficient' in msg or 'balance' in msg:
            self.adjustments[name] = {'reduce_size': True}
            logger.info(f"🔧 [{name}] Enabled size reduction")
            return True
        return False

class CircuitBreaker:
    """Temporarily disable misbehaving adapters."""
    def __init__(self, max_failures: int = 3, reset_timeout: int = 300):
        self.failures: Dict[str, int] = {}
        self.disabled_until: Dict[str, float] = {}
        self.max_failures = max_failures
        self.reset_timeout = reset_timeout

    def record_failure(self, name: str):
        count = self.failures.get(name, 0) + 1
        self.failures[name] = count
        if count >= self.max_failures:
            until = time.time() + self.reset_timeout
            self.disabled_until[name] = until
            logger.error(f"🔴 [{name}] Circuit breaker activated until {time.ctime(until)}")

    def is_enabled(self, name: str) -> bool:
        until = self.disabled_until.get(name)
        if until and time.time() < until:
            return False
        if until and time.time() >= until:
            self.failures[name] = 0
            del self.disabled_until[name]
            logger.info(f"🟢 [{name}] Circuit breaker reset")
        return True

    def record_success(self, name: str):
        """Record successful operation to reset failure count"""
        if name in self.failures:
            self.failures[name] = 0

class RateLimiter:
    """Token bucket rate limiter with burst capability."""
    def __init__(self, rate: float, per: float = 1.0, burst: int = 5):
        self._limit = rate
        self._per = per
        self._allowance = rate
        self._last = time.time()
        self._lock = asyncio.Lock()
        self._burst = burst
        self._tokens = burst

    async def acquire(self):
        async with self._lock:
            current = time.time()
            elapsed = current - self._last
            self._last = current
            
            # Refill tokens
            self._allowance += elapsed * (self._limit / self._per)
            if self._allowance > self._limit:
                self._allowance = self._limit
            
            # Use burst tokens first
            if self._tokens > 0:
                self._tokens -= 1
                return
                
            if self._allowance < 1.0:
                wait = (1.0 - self._allowance) * (self._per / self._limit)
                await asyncio.sleep(wait)
                self._allowance = 0
                return
            self._allowance -= 1.0

# ----------------------------------------------------------------------------
# Retry decorator
# ----------------------------------------------------------------------------
def retry(max_attempts: int = 3, base_backoff: float = 1.0):
    def decorator(func):
        async def wrapper(self, *args, **kwargs):
            backoff = base_backoff
            name = self.name
            for attempt in range(1, max_attempts + 1):
                if not self.circuit.is_enabled(name):
                    raise RuntimeError(f"[{name}] Circuit breaker open, skipping operation")
                try:
                    result = await func(self, *args, **kwargs)
                    self.circuit.record_success(name)
                    return result
                except Exception as e:
                    logger.warning(f"⚠️ [{name}] Attempt {attempt}/{max_attempts} failed: {e}")
                    self.circuit.record_failure(name)
                    solved = await self.solver.solve(name, e)
                    if solved:
                        logger.info(f"🔧 [{name}] Error auto-resolved, retrying...")
                        continue
                    if attempt == max_attempts:
                        logger.error(f"❌ [{name}] Failed after {max_attempts} attempts")
                        raise
                    await asyncio.sleep(backoff)
                    backoff *= 2
        return wrapper
    return decorator

# ----------------------------------------------------------------------------
# Adapter interface
# ----------------------------------------------------------------------------
class ExchangeAdapter(ABC):
    def __init__(
        self,
        solver: ErrorSolver,
        circuit: CircuitBreaker,
        rate_limiter: RateLimiter,
        session: aiohttp.ClientSession
    ):
        self.name = self.__class__.__name__.replace('Adapter', '').upper()
        self.solver = solver
        self.circuit = circuit
        self.rate_limiter = rate_limiter
        self.session = session
        self.last_prices = {}
        self.last_update = 0

    @abstractmethod
    async def fetch_prices(self, symbols: List[str]) -> Dict[str, float]:
        """Fetch latest prices."""
        raise NotImplementedError

    @abstractmethod
    async def fetch_orderbook(self, symbol: str) -> Dict[str, Any]:
        """Fetch order book."""
        raise NotImplementedError

    @abstractmethod
    async def get_limits(self, symbol: str) -> Dict[str, Any]:
        """Fetch trading limits."""
        raise NotImplementedError

    @abstractmethod
    async def execute_order(self, order: Dict[str, Any]) -> Dict[str, Any]:
        """Place an order."""
        raise NotImplementedError

    @abstractmethod
    async def fetch_balances(self) -> Dict[str, float]:
        """Fetch account balances."""
        raise NotImplementedError

    @abstractmethod
    async def fetch_positions(self) -> List[Dict[str, Any]]:
        """Fetch open positions."""
        raise NotImplementedError

    @abstractmethod
    async def fetch_open_orders(self, symbol: Optional[str] = None) -> List[Dict[str, Any]]:
        """Fetch open orders."""
        raise NotImplementedError

    @abstractmethod
    async def cancel_order(self, order_id: str) -> Dict[str, Any]:
        """Cancel an order."""
        raise NotImplementedError

    @abstractmethod
    async def fetch_trade_history(self, symbol: str, limit: int = 100) -> List[Dict[str, Any]]:
        """Fetch trade history."""
        raise NotImplementedError

    async def transform_market_context(
        self,
        prices: Dict[str, float],
        sentiment: Dict[str, float],
        unified_sentiment: float,
        additional_data: Dict[str, Any] = {}
    ) -> Dict[str, Any]:
        """
        Transform raw price and sentiment data into exchange-specific context features.
        """
        features: Dict[str, Any] = {}
        
        # Price ratios and momentum
        avg_price = sum(prices.values()) / len(prices) if prices else 0
        for sym, p in prices.items():
            features[f"{sym}_ratio"] = p / avg_price if avg_price else 0
            
            # Calculate momentum if we have previous prices
            if sym in self.last_prices:
                momentum = (p - self.last_prices[sym]) / self.last_prices[sym] if self.last_prices[sym] else 0
                features[f"{sym}_momentum"] = momentum
        
        # Update price cache
        self.last_prices.update(prices)
        self.last_update = time.time()
        
        # Include sentiment data
        features['unified_sentiment'] = unified_sentiment
        features['sentiment_factors'] = sentiment
        
        # Exchange-specific features
        features['exchange'] = self.name.lower()
        features['timestamp'] = time.time()
        
        # Merge additional data
        features.update(additional_data)
        
        return features

    def get_cached_prices(self, max_age: int = 30) -> Dict[str, float]:
        """Get cached prices if they're recent enough"""
        if time.time() - self.last_update < max_age:
            return self.last_prices.copy()
        return {}

# ----------------------------------------------------------------------------
# CoinbaseAdapter - Enhanced for AutoGPT Trader
# ----------------------------------------------------------------------------
class CoinbaseAdapter(ExchangeAdapter):
    BASE_URL = 'https://api.exchange.coinbase.com'

    def __init__(self, solver, circuit, rate_limiter, session):
        super().__init__(solver, circuit, rate_limiter, session)
        self.api_key = os.getenv('COINBASE_API_KEY')
        self.api_secret = os.getenv('COINBASE_API_SECRET')
        self.passphrase = os.getenv('COINBASE_PASSPHRASE')

    def _get_auth_headers(self, method: str, path: str, body: str = '') -> Dict[str, str]:
        """Generate Coinbase Pro authentication headers"""
        if not all([self.api_key, self.api_secret, self.passphrase]):
            return {}

        timestamp = str(time.time())
        message = timestamp + method + path + body
        signature = hmac.new(
            self.api_secret.encode(),
            message.encode(),
            hashlib.sha256
        ).hexdigest()

        return {
            'CB-ACCESS-KEY': self.api_key,
            'CB-ACCESS-SIGN': signature,
            'CB-ACCESS-TIMESTAMP': timestamp,
            'CB-ACCESS-PASSPHRASE': self.passphrase,
            'Content-Type': 'application/json'
        }

    @retry()
    async def fetch_prices(self, symbols: List[str]) -> Dict[str, float]:
        await self.rate_limiter.acquire()
        prices = {}

        for sym in symbols:
            try:
                # Convert symbol format if needed (BTC-USD, ETH-USD, etc.)
                coinbase_symbol = sym.replace('USDT', 'USD').replace('/', '-')
                url = f"{self.BASE_URL}/products/{coinbase_symbol}/ticker"

                timeout = self.solver.adjustments.get(self.name, {}).get('timeout', 10)
                async with self.session.get(url, timeout=timeout) as resp:
                    if resp.status == 200:
                        data = await resp.json()
                        prices[sym] = float(data['price'])
                    else:
                        logger.warning(f"⚠️ [COINBASE] Failed to fetch {sym}: HTTP {resp.status}")
            except Exception as e:
                logger.warning(f"⚠️ [COINBASE] Error fetching {sym}: {e}")
                continue

        logger.info(f"📊 [COINBASE] Fetched {len(prices)} prices")
        return prices

    @retry()
    async def fetch_orderbook(self, symbol: str) -> Dict[str, Any]:
        await self.rate_limiter.acquire()
        coinbase_symbol = symbol.replace('USDT', 'USD').replace('/', '-')
        url = f"{self.BASE_URL}/products/{coinbase_symbol}/book?level=2"

        async with self.session.get(url) as resp:
            resp.raise_for_status()
            data = await resp.json()

            # Standardize format
            return {
                'bids': [[float(bid[0]), float(bid[1])] for bid in data.get('bids', [])],
                'asks': [[float(ask[0]), float(ask[1])] for ask in data.get('asks', [])],
                'timestamp': time.time(),
                'symbol': symbol
            }

    @retry()
    async def get_limits(self, symbol: str) -> Dict[str, Any]:
        await self.rate_limiter.acquire()
        coinbase_symbol = symbol.replace('USDT', 'USD').replace('/', '-')
        url = f"{self.BASE_URL}/products/{coinbase_symbol}"

        async with self.session.get(url) as resp:
            resp.raise_for_status()
            data = await resp.json()

            return {
                'min_size': float(data.get('base_min_size', '0.001')),
                'max_size': float(data.get('base_max_size', '1000000')),
                'step_size': float(data.get('base_increment', '0.00000001')),
                'min_price': float(data.get('quote_increment', '0.01')),
                'min_notional': 1.0  # Coinbase minimum $1
            }

    @retry()
    async def execute_order(self, order: Dict[str, Any]) -> Dict[str, Any]:
        await self.rate_limiter.acquire()

        # Apply size reduction if needed
        if self.solver.adjustments.get(self.name, {}).get('reduce_size'):
            if 'size' in order:
                order['size'] = str(float(order['size']) * 0.9)  # Reduce by 10%

        url = f"{self.BASE_URL}/orders"
        headers = self._get_auth_headers('POST', '/orders', str(order))

        async with self.session.post(url, json=order, headers=headers) as resp:
            resp.raise_for_status()
            result = await resp.json()

            logger.info(f"✅ [COINBASE] Order executed: {result.get('id', 'unknown')}")
            return result

    @retry()
    async def fetch_balances(self) -> Dict[str, float]:
        await self.rate_limiter.acquire()
        url = f"{self.BASE_URL}/accounts"
        headers = self._get_auth_headers('GET', '/accounts')

        async with self.session.get(url, headers=headers) as resp:
            resp.raise_for_status()
            data = await resp.json()

            balances = {}
            for account in data:
                currency = account['currency']
                balance = float(account['balance'])
                if balance > 0:
                    balances[currency] = balance

            logger.info(f"💰 [COINBASE] Fetched {len(balances)} non-zero balances")
            return balances

    @retry()
    async def fetch_positions(self) -> List[Dict[str, Any]]:
        # Coinbase Pro is spot trading only
        return []

    @retry()
    async def fetch_open_orders(self, symbol: Optional[str] = None) -> List[Dict[str, Any]]:
        await self.rate_limiter.acquire()
        url = f"{self.BASE_URL}/orders"
        params = {}
        if symbol:
            params['product_id'] = symbol.replace('USDT', 'USD').replace('/', '-')

        headers = self._get_auth_headers('GET', '/orders')
        async with self.session.get(url, params=params, headers=headers) as resp:
            resp.raise_for_status()
            return await resp.json()

    @retry()
    async def cancel_order(self, order_id: str) -> Dict[str, Any]:
        await self.rate_limiter.acquire()
        url = f"{self.BASE_URL}/orders/{order_id}"
        headers = self._get_auth_headers('DELETE', f'/orders/{order_id}')

        async with self.session.delete(url, headers=headers) as resp:
            resp.raise_for_status()
            logger.info(f"🗑️ [COINBASE] Cancelled order: {order_id}")
            return {'id': order_id, 'status': 'cancelled'}

    @retry()
    async def fetch_trade_history(self, symbol: str, limit: int = 100) -> List[Dict[str, Any]]:
        await self.rate_limiter.acquire()
        url = f"{self.BASE_URL}/fills"
        params = {
            'product_id': symbol.replace('USDT', 'USD').replace('/', '-'),
            'limit': min(limit, 100)
        }

        headers = self._get_auth_headers('GET', '/fills')
        async with self.session.get(url, params=params, headers=headers) as resp:
            resp.raise_for_status()
            return await resp.json()


# ----------------------------------------------------------------------------
# Unified Exchange Adapters - Main Orchestrator Class
# ----------------------------------------------------------------------------
class UnifiedExchangeAdapters:
    """
    Unified Exchange Adapters System
    Orchestrates multiple exchange adapters with standardized interface
    """

    def __init__(self, exchanges: Dict[str, Any], config: Dict[str, Any] = None):
        """Initialize unified exchange adapters"""
        self.exchanges = exchanges or {}
        self.config = config or {}
        self.adapters = {}
        self.session = None
        self.solver = ErrorSolver()
        self.circuit = CircuitBreaker()
        self.rate_limiter = RateLimiter(rate=100.0, per=60.0, burst=10)  # 100 requests per minute with burst of 10

        logger.info("🔗 [UNIFIED-ADAPTERS] Unified exchange adapters initialized")

    async def initialize(self):
        """Initialize all adapters"""
        try:
            # Create session
            self.session = aiohttp.ClientSession()

            # Initialize adapters for each exchange
            for exchange_name, exchange_client in self.exchanges.items():
                if exchange_name.lower() == 'coinbase':
                    adapter = CoinbaseAdapter(
                        self.solver, self.circuit, self.rate_limiter, self.session
                    )
                    self.adapters[exchange_name] = adapter
                    logger.info(f"✅ [UNIFIED-ADAPTERS] {exchange_name} adapter initialized")

            logger.info(f"🔗 [UNIFIED-ADAPTERS] {len(self.adapters)} adapters initialized")

        except Exception as e:
            logger.error(f"❌ [UNIFIED-ADAPTERS] Initialization failed: {e}")
            raise

    async def get_standardized_balance(self, exchange: str) -> Dict[str, float]:
        """Get standardized balance from exchange"""
        try:
            if exchange in self.adapters:
                return await self.adapters[exchange].fetch_balance()
            else:
                logger.warning(f"⚠️ [UNIFIED-ADAPTERS] No adapter for {exchange}")
                return {}
        except Exception as e:
            logger.error(f"❌ [UNIFIED-ADAPTERS] Balance fetch failed for {exchange}: {e}")
            return {}

    async def place_standardized_order(self, exchange: str, symbol: str, side: str,
                                     amount: float, price: float = None) -> Dict[str, Any]:
        """Place standardized order on exchange"""
        try:
            if exchange in self.adapters:
                return await self.adapters[exchange].place_order(symbol, side, amount, price)
            else:
                logger.warning(f"⚠️ [UNIFIED-ADAPTERS] No adapter for {exchange}")
                return {'error': f'No adapter for {exchange}'}
        except Exception as e:
            logger.error(f"❌ [UNIFIED-ADAPTERS] Order placement failed for {exchange}: {e}")
            return {'error': str(e)}

    async def get_consolidated_balance(self) -> Dict[str, float]:
        """Get consolidated balance across all exchanges"""
        try:
            consolidated = {}

            for exchange_name in self.adapters:
                try:
                    balance = await self.get_standardized_balance(exchange_name)
                    for currency, amount in balance.items():
                        if currency in consolidated:
                            consolidated[currency] += amount
                        else:
                            consolidated[currency] = amount
                except Exception as e:
                    logger.warning(f"⚠️ [UNIFIED-ADAPTERS] Failed to get balance from {exchange_name}: {e}")

            return consolidated

        except Exception as e:
            logger.error(f"❌ [UNIFIED-ADAPTERS] Consolidated balance failed: {e}")
            return {}

    async def shutdown(self):
        """Shutdown all adapters"""
        try:
            if self.session:
                await self.session.close()
            logger.info("🔗 [UNIFIED-ADAPTERS] Shutdown complete")
        except Exception as e:
            logger.error(f"❌ [UNIFIED-ADAPTERS] Shutdown error: {e}")


# Export the main class
__all__ = ['UnifiedExchangeAdapters', 'ExchangeAdapter', 'CoinbaseAdapter']
