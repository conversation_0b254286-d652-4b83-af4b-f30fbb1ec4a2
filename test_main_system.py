#!/usr/bin/env python3
"""
Test Main System Initialization
Test the comprehensive system initialization without full validation
"""

import asyncio
import logging
import sys
import os
import time
from pathlib import Path

# Add project root to path
PROJECT_ROOT = Path(__file__).parent
sys.path.insert(0, str(PROJECT_ROOT))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_main_system():
    """Test main system initialization"""
    logger.info("🚀 [MAIN-TEST] Testing main system initialization...")
    
    try:
        # Test pandas import first
        import pandas as pd
        logger.info(f"✅ [MAIN-TEST] Pandas available: {pd.__version__}")
        
        # Test neural imports
        logger.info("🧠 [MAIN-TEST] Testing neural imports...")
        from src.neural import RLAgentManager, HybridTradingAgent, PricePredictor
        logger.info("✅ [MAIN-TEST] Core neural components imported successfully")
        
        # Test main system import
        logger.info("🏗️ [MAIN-TEST] Testing main system import...")
        from main import ComprehensiveLiveTradingSystem
        logger.info("✅ [MAIN-TEST] Main system class imported successfully")
        
        # Test system initialization
        logger.info("🔧 [MAIN-TEST] Testing system initialization...")
        start_time = time.time()
        
        trading_system = ComprehensiveLiveTradingSystem()
        init_time = time.time() - start_time
        
        logger.info(f"✅ [MAIN-TEST] System initialized in {init_time:.2f}s")
        
        # Test component dictionaries
        required_attributes = [
            'neural_components', 'trading_engines', 'exchange_clients',
            'data_feeds', 'optimization_engines', 'learning_systems',
            'monitoring_systems', 'risk_managers', 'performance_trackers'
        ]
        
        for attr in required_attributes:
            if hasattr(trading_system, attr):
                logger.info(f"✅ [MAIN-TEST] {attr}: Available")
            else:
                logger.error(f"❌ [MAIN-TEST] {attr}: Missing")
        
        # Test comprehensive initialization (without full setup)
        logger.info("🚀 [MAIN-TEST] Testing comprehensive initialization...")
        
        # This should work without pandas issues
        try:
            await trading_system.initialize_comprehensive_system()
            logger.info("✅ [MAIN-TEST] Comprehensive initialization successful!")
            
            # Check component counts
            logger.info(f"📊 [MAIN-TEST] Total Components: {len(trading_system.components)}")
            logger.info(f"🧠 [MAIN-TEST] Neural Components: {len(trading_system.neural_components)}")
            logger.info(f"⚡ [MAIN-TEST] Trading Engines: {len(trading_system.trading_engines)}")
            logger.info(f"🔗 [MAIN-TEST] Exchange Clients: {len(trading_system.exchange_clients)}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ [MAIN-TEST] Comprehensive initialization failed: {e}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        logger.error(f"❌ [MAIN-TEST] System test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run main system test"""
    try:
        success = await test_main_system()
        
        if success:
            logger.info("🎉 [SUCCESS] Main system test passed!")
            return 0
        else:
            logger.error("❌ [FAILURE] Main system test failed!")
            return 1
            
    except Exception as e:
        logger.error(f"❌ [CRITICAL] Main system test execution failed: {e}")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
