#!/usr/bin/env python3
"""
Exchange Integration Test
Tests both Bybit (working) and Coinbase (newly fixed) integration
"""

import asyncio
import logging
import sys
import os
import time
from pathlib import Path

# Add project root to path
PROJECT_ROOT = Path(__file__).parent
sys.path.insert(0, str(PROJECT_ROOT))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ExchangeIntegrationTest:
    """Test exchange integration for both Bybit and Coinbase"""
    
    def __init__(self):
        self.test_results = {}
        self.total_tests = 0
        self.passed_tests = 0
        self.failed_tests = 0
        
    async def run_exchange_tests(self):
        """Run comprehensive exchange integration tests"""
        logger.info("🔗 [EXCHANGE-TEST] Starting Exchange Integration Tests...")
        
        # Load environment and credentials
        await self.setup_credentials()
        
        # Test 1: Bybit Integration (preserve working functionality)
        await self.test_bybit_integration()
        
        # Test 2: Coinbase Integration (newly fixed)
        await self.test_coinbase_integration()
        
        # Test 3: Multi-Exchange Coordination
        await self.test_multi_exchange_coordination()
        
        # Generate final report
        self.generate_exchange_report()
        
        return self.passed_tests == self.total_tests
    
    async def setup_credentials(self):
        """Setup credentials using the fixed decryption system"""
        logger.info("🔐 [EXCHANGE-TEST] Setting up credentials...")
        
        try:
            # Load environment variables
            from dotenv import load_dotenv
            load_dotenv('.env')
            
            # Use HybridCrypto for proper decryption
            try:
                from src.utils.cryptpography.hybrid import HybridCrypto
                crypto = HybridCrypto('src/utils/cryptography/private.pem')
                decrypt_value = crypto.decrypt_value
                logger.info("✅ [EXCHANGE-TEST] Using HybridCrypto for decryption")
                
                # Decrypt Coinbase credentials
                coinbase_api_key = os.getenv('ENCRYPTED_COINBASE_API_KEY_NAME')
                coinbase_private_key = os.getenv('ENCRYPTED_COINBASE_PRIVATE_KEY')
                
                if coinbase_api_key:
                    decrypted_key = decrypt_value(coinbase_api_key)
                    os.environ['COINBASE_API_KEY_NAME'] = decrypted_key
                    logger.info("✅ [EXCHANGE-TEST] Coinbase API key decrypted successfully")
                
                if coinbase_private_key:
                    decrypted_private_key = decrypt_value(coinbase_private_key)
                    os.environ['COINBASE_PRIVATE_KEY'] = decrypted_private_key
                    logger.info("✅ [EXCHANGE-TEST] Coinbase private key decrypted successfully")
                
                # Bybit credentials should already be available (preserve working functionality)
                bybit_key = os.getenv('BYBIT_API_KEY')
                bybit_secret = os.getenv('BYBIT_API_SECRET')
                
                if bybit_key and bybit_secret:
                    logger.info("✅ [EXCHANGE-TEST] Bybit credentials available")
                else:
                    logger.warning("⚠️ [EXCHANGE-TEST] Bybit credentials not found")
                
            except Exception as e:
                logger.error(f"❌ [EXCHANGE-TEST] Credential setup failed: {e}")
                
        except Exception as e:
            logger.error(f"❌ [EXCHANGE-TEST] Setup failed: {e}")
    
    async def test_bybit_integration(self):
        """Test Bybit integration (preserve working functionality)"""
        logger.info("🟡 [BYBIT-TEST] Testing Bybit integration...")
        
        self.total_tests += 1
        try:
            # Import Bybit client
            from src.exchanges.bybit_client_fixed import BybitClientFixed
            
            # Get credentials
            api_key = os.getenv('BYBIT_API_KEY')
            api_secret = os.getenv('BYBIT_API_SECRET')
            
            if not api_key or not api_secret:
                raise ValueError("Bybit credentials not available")
            
            # Initialize client
            bybit_client = BybitClientFixed(
                api_key=api_key,
                api_secret=api_secret,
                testnet=False  # LIVE TRADING
            )
            
            # Test basic connectivity using balance check
            start_time = time.time()
            balance = await bybit_client.get_balance("USDT")
            response_time = time.time() - start_time

            if balance is not None and response_time < 2.0:  # Less than 2 seconds (adjusted for real API)
                self.test_results["bybit_integration"] = "PASS"
                self.passed_tests += 1
                logger.info(f"✅ [BYBIT-TEST] Integration successful ({response_time:.3f}s)")
            else:
                self.test_results["bybit_integration"] = f"FAIL: Slow response ({response_time:.3f}s)"
                self.failed_tests += 1
                logger.error(f"❌ [BYBIT-TEST] Integration failed")
            
        except Exception as e:
            self.test_results["bybit_integration"] = f"FAIL: {e}"
            self.failed_tests += 1
            logger.error(f"❌ [BYBIT-TEST] Integration failed: {e}")
    
    async def test_coinbase_integration(self):
        """Test Coinbase integration (newly fixed)"""
        logger.info("🔵 [COINBASE-TEST] Testing Coinbase integration...")
        
        self.total_tests += 1
        try:
            # Import Coinbase client
            from src.exchanges.coinbase_enhanced_client import CoinbaseEnhancedClient
            
            # Get credentials
            api_key_name = os.getenv('COINBASE_API_KEY_NAME')
            private_key_pem = os.getenv('COINBASE_PRIVATE_KEY')
            key_id = os.getenv('COINBASE_KEY_ID')

            if not api_key_name or not private_key_pem:
                raise ValueError("Coinbase credentials not available after decryption")

            # Verify credentials are properly decrypted
            if api_key_name.startswith('gAAAAA') or private_key_pem.startswith('gAAAAA'):
                raise ValueError("Coinbase credentials still encrypted")

            # Initialize client with correct parameters
            coinbase_client = CoinbaseEnhancedClient(
                api_key_name=api_key_name,
                private_key_pem=private_key_pem,
                key_id=key_id or api_key_name.split('/')[-1]  # Extract key_id from api_key_name if not available
            )
            
            # Test basic connectivity
            start_time = time.time()
            # Test a simple API call - check authentication status
            try:
                # Try to get authentication status
                auth_status = coinbase_client.get_authentication_status()
                response_time = time.time() - start_time
                success = auth_status is not None and auth_status.get('authenticated', False)

                if not success:
                    # Try to get balances as fallback test
                    balances = coinbase_client.get_all_balances()
                    success = balances is not None and len(balances) > 0

            except Exception as e:
                response_time = time.time() - start_time
                success = False
                logger.warning(f"⚠️ [COINBASE-TEST] API call failed: {e}")

            if success and response_time < 3.0:  # Less than 3 seconds
                self.test_results["coinbase_integration"] = "PASS"
                self.passed_tests += 1
                logger.info(f"✅ [COINBASE-TEST] Integration successful ({response_time:.3f}s)")
            else:
                self.test_results["coinbase_integration"] = f"FAIL: Slow response ({response_time:.3f}s)"
                self.failed_tests += 1
                logger.error(f"❌ [COINBASE-TEST] Integration failed")
            
        except Exception as e:
            self.test_results["coinbase_integration"] = f"FAIL: {e}"
            self.failed_tests += 1
            logger.error(f"❌ [COINBASE-TEST] Integration failed: {e}")
    
    async def test_multi_exchange_coordination(self):
        """Test multi-exchange coordination"""
        logger.info("🔗 [MULTI-EXCHANGE-TEST] Testing multi-exchange coordination...")
        
        self.total_tests += 1
        try:
            # Check if both exchanges are working
            bybit_working = self.test_results.get("bybit_integration") == "PASS"
            coinbase_working = self.test_results.get("coinbase_integration") == "PASS"
            
            if bybit_working and coinbase_working:
                self.test_results["multi_exchange_coordination"] = "PASS"
                self.passed_tests += 1
                logger.info("✅ [MULTI-EXCHANGE-TEST] Both exchanges working - coordination possible")
            elif bybit_working:
                self.test_results["multi_exchange_coordination"] = "PARTIAL: Bybit only"
                self.passed_tests += 1
                logger.info("⚠️ [MULTI-EXCHANGE-TEST] Bybit working, Coinbase issues")
            else:
                self.test_results["multi_exchange_coordination"] = "FAIL: No working exchanges"
                self.failed_tests += 1
                logger.error("❌ [MULTI-EXCHANGE-TEST] No working exchanges")
            
        except Exception as e:
            self.test_results["multi_exchange_coordination"] = f"FAIL: {e}"
            self.failed_tests += 1
            logger.error(f"❌ [MULTI-EXCHANGE-TEST] Coordination test failed: {e}")
    
    def generate_exchange_report(self):
        """Generate comprehensive exchange integration report"""
        logger.info("📊 [EXCHANGE-TEST] Generating exchange integration report...")
        
        success_rate = (self.passed_tests / self.total_tests * 100) if self.total_tests > 0 else 0
        
        print("\n" + "="*80)
        print("🔗 EXCHANGE INTEGRATION TEST REPORT")
        print("="*80)
        print(f"📊 Total Tests: {self.total_tests}")
        print(f"✅ Passed: {self.passed_tests}")
        print(f"❌ Failed: {self.failed_tests}")
        print(f"📈 Success Rate: {success_rate:.1f}%")
        print("="*80)
        
        # Exchange status
        print("\n🔗 EXCHANGE STATUS:")
        for test_name, result in self.test_results.items():
            if "bybit" in test_name:
                icon = "🟡"
            elif "coinbase" in test_name:
                icon = "🔵"
            else:
                icon = "🔗"
            
            status = "✅ PASS" if result == "PASS" else f"❌ {result}"
            print(f"  {icon} {test_name}: {status}")
        
        if self.failed_tests > 0:
            print("\n❌ FAILED TESTS:")
            for test_name, result in self.test_results.items():
                if not result.startswith("PASS"):
                    print(f"  • {test_name}: {result}")
        
        if success_rate >= 90:
            print("\n🎉 EXCELLENT: Multi-exchange integration ready!")
        elif success_rate >= 70:
            print("\n✅ GOOD: Primary exchange working, some issues to resolve!")
        else:
            print("\n❌ CRITICAL: Exchange integration needs attention!")
        
        print("="*80)

async def main():
    """Run exchange integration tests"""
    try:
        test_system = ExchangeIntegrationTest()
        success = await test_system.run_exchange_tests()
        
        if success:
            logger.info("🎉 [SUCCESS] Exchange integration tests completed successfully!")
            return 0
        else:
            logger.error("❌ [FAILURE] Some exchange integration tests failed!")
            return 1
            
    except Exception as e:
        logger.error(f"❌ [CRITICAL] Exchange integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
