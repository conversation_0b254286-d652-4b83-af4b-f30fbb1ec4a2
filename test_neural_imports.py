#!/usr/bin/env python3
"""
Test Neural Component Imports
Identify which neural component is causing the pandas import error
"""

import sys
from pathlib import Path

# Add project root to path
PROJECT_ROOT = Path(__file__).parent
sys.path.insert(0, str(PROJECT_ROOT))

def test_individual_imports():
    """Test each neural component import individually"""
    
    # Test pandas first
    try:
        import pandas as pd
        print("✅ pandas import successful")
    except Exception as e:
        print(f"❌ pandas import failed: {e}")
        return
    
    # Test basic imports
    imports_to_test = [
        ("src.neural.rl_agent", "RLAgentManager"),
        ("src.neural.reinforcement_learning", "ReinforcementLearningAgent"),
        ("src.neural.hybrid_agent", "HybridTradingAgent"),
        ("src.neural.predictors", "QuantumSecureTradingSystem"),
        ("src.neural.market_monitor", "MarketAnomalyDetector"),
        ("src.neural.hyperomtimizer", "BayesianProfitOptimizer"),
        ("src.neural.lstm_processor", "LSTMProcessor"),
        ("src.neural.dark_pool", "DarkPoolRouter"),
        ("src.neural.liquidity", "LiquidityAnalyzer"),
        ("src.neural.arbitrage", "CrossVenueArbitrageDetector"),
        ("src.neural.enhanced_risk_predictor", "EnhancedRiskPredictor"),
        ("src.neural.enhanced_profit_predictor", "EnhancedProfitPredictor"),
        ("src.neural.advanced_neural_strategy", "AdvancedNeuralStrategy"),
    ]

    # Test professional-grade components
    professional_imports = [
        ("src.neural.advanced_lstm_processor", "AdvancedLSTMProcessor"),
        ("src.neural.transformer_trading_model", "TransformerTradingModel"),
        ("src.neural.graph_neural_network", "MarketGraphNeuralNetwork"),
        ("src.neural.variational_autoencoder", "MarketVAE"),
        ("src.neural.neural_architecture_search", "GeneticAlgorithmNAS"),
        ("src.neural.performance_optimizer", "InferenceOptimizer"),
    ]
    
    for module_name, class_name in imports_to_test:
        try:
            module = __import__(module_name, fromlist=[class_name])
            component_class = getattr(module, class_name)
            print(f"✅ {class_name}: SUCCESS")
        except Exception as e:
            print(f"❌ {class_name}: FAILED - {e}")
            if "pandas" in str(e).lower():
                print(f"   🔍 PANDAS ISSUE DETECTED in {class_name}")

    print("\n🏛️ Testing Professional-Grade Components:")
    for module_name, class_name in professional_imports:
        try:
            module = __import__(module_name, fromlist=[class_name])
            component_class = getattr(module, class_name)
            print(f"✅ {class_name}: SUCCESS")
        except Exception as e:
            print(f"❌ {class_name}: FAILED - {e}")
            if "pandas" in str(e).lower():
                print(f"   🔍 PANDAS ISSUE DETECTED in {class_name}")

if __name__ == "__main__":
    test_individual_imports()
