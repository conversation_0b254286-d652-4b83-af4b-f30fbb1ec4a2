"""
Professional-grade system monitoring and health tracking
"""
import time
import logging
import psutil
import threading
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from collections import deque, defaultdict

logger = logging.getLogger(__name__)

@dataclass
class SystemMetrics:
    """System performance metrics"""
    cpu_percent: float
    memory_percent: float
    disk_usage: float
    network_io: Dict[str, int]
    process_count: int
    uptime: float
    timestamp: float

@dataclass
class TradingMetrics:
    """Trading system specific metrics"""
    active_strategies: int
    total_positions: int
    api_calls_per_minute: float
    error_rate: float
    avg_response_time: float
    last_trade_time: float

class SystemMonitor:
    """Enterprise-grade system monitoring"""
    
    def __init__(self):
        self.metrics_history: deque = deque(maxlen=1000)
        self.trading_metrics_history: deque = deque(maxlen=1000)
        self.api_call_times: deque = deque(maxlen=100)
        self.error_count = 0
        self.total_requests = 0
        self.start_time = time.time()
        self.monitoring_active = False
        self.monitor_thread = None
        
        logger.info("📊 [SYSTEM-MONITOR] Professional system monitoring initialized")
    
    def start_monitoring(self, interval: float = 30.0):
        """Start continuous system monitoring"""
        if self.monitoring_active:
            return
        
        self.monitoring_active = True
        self.monitor_thread = threading.Thread(target=self._monitoring_loop, args=(interval,))
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
        
        logger.info(f"🔍 [MONITOR] System monitoring started with {interval}s interval")
    
    def stop_monitoring(self):
        """Stop system monitoring"""
        self.monitoring_active = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5.0)
        logger.info("🛑 [MONITOR] System monitoring stopped")
    
    def _monitoring_loop(self, interval: float):
        """Main monitoring loop"""
        while self.monitoring_active:
            try:
                self._collect_system_metrics()
                time.sleep(interval)
            except Exception as e:
                logger.error(f"❌ [MONITOR] Error in monitoring loop: {e}")
                time.sleep(interval)
    
    def _collect_system_metrics(self):
        """Collect current system metrics"""
        try:
            # System metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            network = psutil.net_io_counters()
            
            metrics = SystemMetrics(
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                disk_usage=disk.percent,
                network_io={
                    'bytes_sent': network.bytes_sent,
                    'bytes_recv': network.bytes_recv
                },
                process_count=len(psutil.pids()),
                uptime=time.time() - self.start_time,
                timestamp=time.time()
            )
            
            self.metrics_history.append(metrics)
            
            # Log critical alerts
            if cpu_percent > 80:
                logger.warning(f"⚠️ [ALERT] High CPU usage: {cpu_percent:.1f}%")
            if memory.percent > 85:
                logger.warning(f"⚠️ [ALERT] High memory usage: {memory.percent:.1f}%")
            if disk.percent > 90:
                logger.warning(f"⚠️ [ALERT] High disk usage: {disk.percent:.1f}%")
                
        except Exception as e:
            logger.error(f"❌ [MONITOR] Error collecting system metrics: {e}")
    
    def record_api_call(self, response_time: float, success: bool = True):
        """Record API call metrics"""
        self.api_call_times.append(response_time)
        self.total_requests += 1
        if not success:
            self.error_count += 1
    
    def record_trading_metrics(self, active_strategies: int, total_positions: int, last_trade_time: float):
        """Record trading-specific metrics"""
        # Calculate API metrics
        current_time = time.time()
        recent_calls = [t for t in self.api_call_times if current_time - t < 60]
        api_calls_per_minute = len(recent_calls)
        
        error_rate = (self.error_count / self.total_requests * 100) if self.total_requests > 0 else 0
        avg_response_time = sum(self.api_call_times) / len(self.api_call_times) if self.api_call_times else 0
        
        trading_metrics = TradingMetrics(
            active_strategies=active_strategies,
            total_positions=total_positions,
            api_calls_per_minute=api_calls_per_minute,
            error_rate=error_rate,
            avg_response_time=avg_response_time,
            last_trade_time=last_trade_time
        )
        
        self.trading_metrics_history.append(trading_metrics)
    
    def get_system_health(self) -> Dict[str, Any]:
        """Get comprehensive system health report"""
        if not self.metrics_history:
            return {"status": "no_data", "message": "No metrics collected yet"}
        
        latest = self.metrics_history[-1]
        
        # Determine health status
        health_score = 100
        alerts = []
        
        if latest.cpu_percent > 80:
            health_score -= 20
            alerts.append(f"High CPU: {latest.cpu_percent:.1f}%")
        
        if latest.memory_percent > 85:
            health_score -= 25
            alerts.append(f"High Memory: {latest.memory_percent:.1f}%")
        
        if latest.disk_usage > 90:
            health_score -= 15
            alerts.append(f"High Disk: {latest.disk_usage:.1f}%")
        
        # Determine status
        if health_score >= 80:
            status = "healthy"
        elif health_score >= 60:
            status = "warning"
        else:
            status = "critical"
        
        return {
            "status": status,
            "health_score": health_score,
            "alerts": alerts,
            "metrics": {
                "cpu_percent": latest.cpu_percent,
                "memory_percent": latest.memory_percent,
                "disk_usage": latest.disk_usage,
                "uptime_hours": latest.uptime / 3600,
                "process_count": latest.process_count
            },
            "api_metrics": {
                "total_requests": self.total_requests,
                "error_count": self.error_count,
                "error_rate": (self.error_count / self.total_requests * 100) if self.total_requests > 0 else 0,
                "avg_response_time": sum(self.api_call_times) / len(self.api_call_times) if self.api_call_times else 0
            }
        }
    
    def get_performance_trends(self) -> Dict[str, List]:
        """Get performance trends over time"""
        if len(self.metrics_history) < 2:
            return {"message": "Insufficient data for trends"}
        
        recent_metrics = list(self.metrics_history)[-10:]  # Last 10 readings
        
        return {
            "cpu_trend": [m.cpu_percent for m in recent_metrics],
            "memory_trend": [m.memory_percent for m in recent_metrics],
            "timestamps": [m.timestamp for m in recent_metrics],
            "trading_metrics": [
                {
                    "active_strategies": tm.active_strategies,
                    "total_positions": tm.total_positions,
                    "api_calls_per_minute": tm.api_calls_per_minute,
                    "error_rate": tm.error_rate
                }
                for tm in list(self.trading_metrics_history)[-10:]
            ]
        }

# Global system monitor instance
system_monitor = SystemMonitor()
