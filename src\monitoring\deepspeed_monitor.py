# backend/src/monitoring/deepspeed_monitor.py
import json
import subprocess
import logging
from typing import Dict, Any
import psutil
import time

class DeepSpeedMonitor:
    def __init__(self, config_path: str = "backend/config/deepspeed_config.json"):
        self.logger = logging.getLogger("deepspeed.monitor")
        self.config = self._load_config(config_path)

    def _load_config(self, path: str) -> Dict[str, Any]:
        try:
            with open(path) as f:
                return json.load(f)
        except FileNotFoundError:
            # Return default configuration if file doesn't exist
            self.logger.warning(f"Config file {path} not found, using default configuration")
            return {
                "thresholds": {
                    "cpu_alert": 80,
                    "gpu_alert": 85
                },
                "defaults": {
                    "train_batch_size": 32
                },
                "min_batch_size": 8,
                "monitoring_interval": 30
            }
    
    def _get_gpu_stats(self) -> Dict[str, float]:
        try:
            result = subprocess.run(
                ["nvidia-smi", "--query-gpu=utilization.gpu,memory.used,memory.total",
                 "--format=csv,noheader,nounits"],
                capture_output=True, text=True
            )
            gpu_data = result.stdout.strip().split(',')
            return {
                "gpu_util": float(gpu_data[0]),
                "mem_used": float(gpu_data[1]),
                "mem_total": float(gpu_data[2])
            }
        except Exception as e:
            self.logger.error(f"GPU monitoring failed: {e}")
            return {"gpu_util": 0, "mem_used": 0, "mem_total": 1}
    
    def _adjust_deepspeed_parameters(self, metrics: Dict[str, float]):
        adjustments = {}
        
        # CPU protection
        if metrics["cpu_load"] > self.config["thresholds"]["cpu_alert"]:
            adjustments["cpu"] = {
                "train_batch_size": max(
                    self.config["min_batch_size"],
                    int(self.config["defaults"]["train_batch_size"] * 0.8)
                ),
                "offload_optimizer": True
            }
        
        # GPU protection
        if metrics["gpu_util"] > self.config["thresholds"]["gpu_alert"]:
            adjustments["gpu"] = {
                "train_batch_size": max(
                    self.config["min_batch_size"],
                    int(self.config["defaults"]["train_batch_size"] * 0.7)
                ),
                "offload_params": True,
                "zero_optimization": {
                    "stage": 2,
                    "contiguous_gradients": True
                }
            }
        
        return adjustments
    
    def run(self):
        while True:
            metrics = {
                "cpu_load": psutil.cpu_percent(),
                **self._get_gpu_stats()
            }
            
            adjustments = self._adjust_deepspeed_parameters(metrics)
            if adjustments:
                self._apply_adjustments(adjustments)
                self.logger.warning(f"Applied protections: {adjustments}")
            
            time.sleep(self.config["monitoring_interval"])

    def _apply_adjustments(self, adjustments: Dict[str, Any]):
        config_path = "backend/config/deepspeed_config.json"
        with open(config_path, 'r+') as f:
            config = json.load(f)
            config["current"] = {**config.get("current", {}), **adjustments}
            f.seek(0)
            json.dump(config, f, indent=2)
            f.truncate()

if __name__ == "__main__":
    monitor = DeepSpeedMonitor()
    monitor.run()