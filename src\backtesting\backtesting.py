"""
Professional-grade backtesting engine for trading strategies
"""
import time
import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from decimal import Decimal
from collections import defaultdict

logger = logging.getLogger(__name__)

@dataclass
class BacktestTrade:
    """Individual backtest trade record"""
    timestamp: float
    symbol: str
    side: str
    quantity: float
    price: float
    strategy: str
    profit_loss: float = 0.0
    fees: float = 0.0

@dataclass
class BacktestResults:
    """Comprehensive backtest results"""
    total_return: float
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    max_drawdown: float
    sharpe_ratio: float
    profit_factor: float
    average_trade: float
    best_trade: float
    worst_trade: float
    total_fees: float
    strategy_performance: Dict[str, Dict] = field(default_factory=dict)

class BacktestingEngine:
    """Enterprise-grade backtesting engine"""
    
    def __init__(self):
        self.historical_data: Dict[str, pd.DataFrame] = {}
        self.trades: List[BacktestTrade] = []
        self.portfolio_value: List[Tuple[float, float]] = []  # (timestamp, value)
        self.initial_capital = 10000.0  # Default starting capital
        self.current_capital = self.initial_capital
        self.positions: Dict[str, float] = defaultdict(float)
        self.strategy_stats: Dict[str, Dict] = defaultdict(dict)
        
        logger.info("🧪 [BACKTESTING] Professional backtesting engine initialized")
    
    def load_historical_data(self, symbol: str, data: pd.DataFrame):
        """Load historical price data for backtesting"""
        self.historical_data[symbol] = data
        logger.info(f"📊 [DATA] Loaded {len(data)} historical records for {symbol}")
    
    def simulate_trade(self, timestamp: float, symbol: str, side: str, 
                      quantity: float, price: float, strategy: str) -> bool:
        """Simulate a trade execution"""
        try:
            trade_value = quantity * price
            fee_rate = 0.001  # 0.1% fee
            fees = trade_value * fee_rate
            
            if side.lower() == 'buy':
                if self.current_capital >= trade_value + fees:
                    self.current_capital -= (trade_value + fees)
                    self.positions[symbol] += quantity
                else:
                    logger.warning(f"⚠️ [BACKTEST] Insufficient capital for {symbol} buy")
                    return False
            else:  # sell
                if self.positions[symbol] >= quantity:
                    self.current_capital += (trade_value - fees)
                    self.positions[symbol] -= quantity
                else:
                    logger.warning(f"⚠️ [BACKTEST] Insufficient position for {symbol} sell")
                    return False
            
            # Calculate P&L for completed round trips
            profit_loss = 0.0
            if side.lower() == 'sell':
                # Simplified P&L calculation
                profit_loss = trade_value - fees - (quantity * price * 1.001)  # Assume avg buy price
            
            trade = BacktestTrade(
                timestamp=timestamp,
                symbol=symbol,
                side=side,
                quantity=quantity,
                price=price,
                strategy=strategy,
                profit_loss=profit_loss,
                fees=fees
            )
            
            self.trades.append(trade)
            self._update_portfolio_value(timestamp)
            self._update_strategy_stats(strategy, profit_loss, fees)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ [BACKTEST] Error simulating trade: {e}")
            return False
    
    def _update_portfolio_value(self, timestamp: float):
        """Update portfolio value tracking"""
        # Calculate total portfolio value (cash + positions)
        total_value = self.current_capital
        
        # Add value of current positions (simplified - using last known prices)
        for symbol, quantity in self.positions.items():
            if symbol in self.historical_data and not self.historical_data[symbol].empty:
                last_price = self.historical_data[symbol]['close'].iloc[-1]
                total_value += quantity * last_price
        
        self.portfolio_value.append((timestamp, total_value))
    
    def _update_strategy_stats(self, strategy: str, profit_loss: float, fees: float):
        """Update strategy-specific statistics"""
        if strategy not in self.strategy_stats:
            self.strategy_stats[strategy] = {
                'total_profit': 0.0,
                'total_fees': 0.0,
                'trade_count': 0,
                'winning_trades': 0
            }
        
        stats = self.strategy_stats[strategy]
        stats['total_profit'] += profit_loss
        stats['total_fees'] += fees
        stats['trade_count'] += 1
        if profit_loss > 0:
            stats['winning_trades'] += 1
    
    def run_backtest(self, start_time: float, end_time: float, 
                    strategies: List[str] = None) -> BacktestResults:
        """Run comprehensive backtest"""
        logger.info(f"🚀 [BACKTEST] Starting backtest from {start_time} to {end_time}")
        
        # Reset state
        self.trades.clear()
        self.portfolio_value.clear()
        self.current_capital = self.initial_capital
        self.positions.clear()
        self.strategy_stats.clear()
        
        # Simulate trading period
        self._simulate_trading_period(start_time, end_time, strategies or [])
        
        # Calculate results
        results = self._calculate_results()
        
        logger.info(f"✅ [BACKTEST] Completed: {results.total_trades} trades, "
                   f"{results.win_rate:.1f}% win rate, {results.total_return:.2f}% return")
        
        return results
    
    def _simulate_trading_period(self, start_time: float, end_time: float, strategies: List[str]):
        """Simulate trading during the specified period"""
        # This is a simplified simulation - in practice, you'd integrate with actual strategy logic
        current_time = start_time
        time_step = 3600  # 1 hour steps
        
        while current_time < end_time:
            # Simulate strategy decisions (placeholder logic)
            for strategy in strategies:
                if np.random.random() > 0.95:  # 5% chance of trade per hour per strategy
                    symbol = np.random.choice(['BTCUSDT', 'ETHUSDT', 'ADAUSDT'])
                    side = np.random.choice(['buy', 'sell'])
                    quantity = np.random.uniform(0.1, 1.0)
                    price = np.random.uniform(100, 50000)  # Simplified price
                    
                    self.simulate_trade(current_time, symbol, side, quantity, price, strategy)
            
            current_time += time_step
    
    def _calculate_results(self) -> BacktestResults:
        """Calculate comprehensive backtest results"""
        if not self.trades:
            return BacktestResults(
                total_return=0.0, total_trades=0, winning_trades=0, losing_trades=0,
                win_rate=0.0, max_drawdown=0.0, sharpe_ratio=0.0, profit_factor=0.0,
                average_trade=0.0, best_trade=0.0, worst_trade=0.0, total_fees=0.0
            )
        
        # Basic metrics
        total_trades = len(self.trades)
        profits = [trade.profit_loss for trade in self.trades if trade.profit_loss != 0]
        winning_trades = len([p for p in profits if p > 0])
        losing_trades = len([p for p in profits if p < 0])
        
        win_rate = (winning_trades / len(profits) * 100) if profits else 0
        total_return = ((self.current_capital - self.initial_capital) / self.initial_capital * 100)
        
        # Advanced metrics
        average_trade = np.mean(profits) if profits else 0
        best_trade = max(profits) if profits else 0
        worst_trade = min(profits) if profits else 0
        total_fees = sum(trade.fees for trade in self.trades)
        
        # Calculate max drawdown
        max_drawdown = self._calculate_max_drawdown()
        
        # Calculate Sharpe ratio (simplified)
        sharpe_ratio = self._calculate_sharpe_ratio(profits)
        
        # Calculate profit factor
        gross_profit = sum(p for p in profits if p > 0)
        gross_loss = abs(sum(p for p in profits if p < 0))
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')
        
        return BacktestResults(
            total_return=total_return,
            total_trades=total_trades,
            winning_trades=winning_trades,
            losing_trades=losing_trades,
            win_rate=win_rate,
            max_drawdown=max_drawdown,
            sharpe_ratio=sharpe_ratio,
            profit_factor=profit_factor,
            average_trade=average_trade,
            best_trade=best_trade,
            worst_trade=worst_trade,
            total_fees=total_fees,
            strategy_performance=dict(self.strategy_stats)
        )
    
    def _calculate_max_drawdown(self) -> float:
        """Calculate maximum drawdown"""
        if len(self.portfolio_value) < 2:
            return 0.0
        
        values = [v[1] for v in self.portfolio_value]
        peak = values[0]
        max_dd = 0.0
        
        for value in values:
            if value > peak:
                peak = value
            drawdown = (peak - value) / peak * 100
            max_dd = max(max_dd, drawdown)
        
        return max_dd
    
    def _calculate_sharpe_ratio(self, profits: List[float]) -> float:
        """Calculate Sharpe ratio"""
        if not profits or len(profits) < 2:
            return 0.0
        
        mean_return = np.mean(profits)
        std_return = np.std(profits)
        
        return mean_return / std_return if std_return > 0 else 0.0
    
    def get_strategy_comparison(self) -> Dict[str, Any]:
        """Get detailed strategy performance comparison"""
        comparison = {}
        
        for strategy, stats in self.strategy_stats.items():
            win_rate = (stats['winning_trades'] / stats['trade_count'] * 100) if stats['trade_count'] > 0 else 0
            avg_profit = stats['total_profit'] / stats['trade_count'] if stats['trade_count'] > 0 else 0
            
            comparison[strategy] = {
                'total_profit': stats['total_profit'],
                'total_fees': stats['total_fees'],
                'net_profit': stats['total_profit'] - stats['total_fees'],
                'trade_count': stats['trade_count'],
                'win_rate': win_rate,
                'average_profit_per_trade': avg_profit
            }
        
        return comparison

class BacktestingOrchestrator:
    """Orchestrates comprehensive backtesting across multiple strategies and timeframes"""

    def __init__(self):
        self.engine = BacktestingEngine()
        self.strategies = []
        self.results_cache = {}

        logger.info("🎭 [BACKTESTING-ORCHESTRATOR] Professional backtesting orchestrator initialized")

    def add_strategy(self, strategy_name: str, strategy_config: Dict[str, Any]):
        """Add a strategy for backtesting"""
        self.strategies.append({
            'name': strategy_name,
            'config': strategy_config
        })
        logger.info(f"📋 [ORCHESTRATOR] Added strategy: {strategy_name}")

    def run_comprehensive_backtest(self, start_time: float, end_time: float) -> Dict[str, Any]:
        """Run comprehensive backtest across all strategies"""
        try:
            logger.info(f"🚀 [ORCHESTRATOR] Starting comprehensive backtest")

            all_results = {}

            for strategy in self.strategies:
                strategy_name = strategy['name']
                logger.info(f"🧪 [ORCHESTRATOR] Testing strategy: {strategy_name}")

                # Run backtest for this strategy
                results = self.engine.run_backtest(start_time, end_time, [strategy_name])
                all_results[strategy_name] = results

                # Cache results
                self.results_cache[strategy_name] = results

            # Generate comparison report
            comparison = self._generate_strategy_comparison(all_results)

            logger.info(f"✅ [ORCHESTRATOR] Comprehensive backtest completed")

            return {
                'individual_results': all_results,
                'comparison': comparison,
                'summary': self._generate_summary(all_results)
            }

        except Exception as e:
            logger.error(f"❌ [ORCHESTRATOR] Error in comprehensive backtest: {e}")
            return {'error': str(e)}

    def _generate_strategy_comparison(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate strategy comparison analysis"""
        try:
            comparison = {}

            for strategy_name, result in results.items():
                comparison[strategy_name] = {
                    'total_return': result.total_return,
                    'win_rate': result.win_rate,
                    'total_trades': result.total_trades,
                    'max_drawdown': result.max_drawdown,
                    'sharpe_ratio': result.sharpe_ratio,
                    'profit_factor': result.profit_factor,
                    'rank_score': self._calculate_rank_score(result)
                }

            # Sort by rank score
            sorted_strategies = sorted(
                comparison.items(),
                key=lambda x: x[1]['rank_score'],
                reverse=True
            )

            return {
                'rankings': sorted_strategies,
                'best_strategy': sorted_strategies[0][0] if sorted_strategies else None,
                'worst_strategy': sorted_strategies[-1][0] if sorted_strategies else None
            }

        except Exception as e:
            logger.error(f"Error generating strategy comparison: {e}")
            return {}

    def _calculate_rank_score(self, result) -> float:
        """Calculate ranking score for a strategy"""
        try:
            # Weighted scoring system
            score = 0.0

            # Return weight: 40%
            score += result.total_return * 0.4

            # Win rate weight: 25%
            score += result.win_rate * 0.25

            # Sharpe ratio weight: 20%
            score += result.sharpe_ratio * 20 * 0.2

            # Drawdown penalty: 15% (negative)
            score -= result.max_drawdown * 0.15

            return score

        except Exception:
            return 0.0

    def _generate_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate overall summary of backtest results"""
        try:
            if not results:
                return {'message': 'No results to summarize'}

            total_strategies = len(results)
            profitable_strategies = sum(1 for r in results.values() if r.total_return > 0)

            avg_return = sum(r.total_return for r in results.values()) / total_strategies
            avg_win_rate = sum(r.win_rate for r in results.values()) / total_strategies
            avg_trades = sum(r.total_trades for r in results.values()) / total_strategies

            return {
                'total_strategies_tested': total_strategies,
                'profitable_strategies': profitable_strategies,
                'profitability_rate': (profitable_strategies / total_strategies * 100) if total_strategies > 0 else 0,
                'average_return': avg_return,
                'average_win_rate': avg_win_rate,
                'average_trades': avg_trades,
                'recommendation': self._generate_recommendation(results)
            }

        except Exception as e:
            logger.error(f"Error generating summary: {e}")
            return {'error': str(e)}

    def _generate_recommendation(self, results: Dict[str, Any]) -> str:
        """Generate trading recommendation based on backtest results"""
        try:
            profitable_count = sum(1 for r in results.values() if r.total_return > 0)
            total_count = len(results)

            if profitable_count == 0:
                return "AVOID: No strategies showed profitability"
            elif profitable_count / total_count >= 0.8:
                return "STRONG BUY: Most strategies are profitable"
            elif profitable_count / total_count >= 0.6:
                return "BUY: Majority of strategies are profitable"
            elif profitable_count / total_count >= 0.4:
                return "HOLD: Mixed results, proceed with caution"
            else:
                return "WEAK: Limited profitability, consider optimization"

        except Exception:
            return "UNKNOWN: Unable to generate recommendation"

# Global instances
backtesting_engine = BacktestingEngine()
backtesting_orchestrator = BacktestingOrchestrator()
