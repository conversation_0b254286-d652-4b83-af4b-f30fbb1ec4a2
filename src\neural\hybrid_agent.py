# neural/hybrid_agent.py
import os
import json
import torch
import numpy as np
from typing import Dict, Optional, List
import asyncio
from datetime import datetime, timezone
import hashlib
import logging
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import rsa
from cryptography.fernet import Fernet

# Local imports
from .dark_pool import <PERSON><PERSON><PERSON><PERSON>out<PERSON>
from .market_monitor import CircuitBreaker
from .liquidity import LiquidityAnalyzer
from .arbitrage import CrossVenueArbitrageDetector
# FIXED: Removed merge conflict markers
try:
    from ..compliance.mifid import MiFIDIILogger
    from ..utils.secure_store import AuditVault
except ImportError:
    MiFIDIILogger = None
    AuditVault = None

try:
    from .som_memory import SOMemory
except ImportError:
    SOMemory = None

logger = logging.getLogger("institutional_agent")
# Import compliance components with fallback
try:
    from ..compliance.mifid import MiFIDIILogger
except ImportError:
    # Fallback MiFIDIILogger for standalone testing
    class MiFIDIILogger:
        def __init__(self, retention_days: int, encryption_key: str):
            self.retention = retention_days
            from cryptography.fernet import Fernet
            self.cipher = Fernet(encryption_key.encode() if isinstance(encryption_key, str) else encryption_key)
        def log(self, record): pass

try:
    from ..utils.secure_store import AuditVault
except ImportError:
    # Fallback AuditVault for standalone testing
    class AuditVault:
        def __init__(self, cloud_storage: bool, immutable_logging: bool): pass
        def store(self, record): pass

logger = logging.getLogger("institutional_agent")

# ---------------
# Support Classes - Define before HybridTradingAgent
# ---------------
class CircuitBreakerActivated(Exception):
    pass

class InsufficientLiquidity(Exception):
    pass

class DarkPoolRouter:
    def __init__(self, venues: List[str], allocation_strategy: str = 'balanced'):
        self.venues = venues
        self.strategies = {
            'balanced': self._balanced_allocation,
            'aggressive': self._aggressive_allocation,
            'round_robin': self._round_robin_allocation,
            'optimal': self._optimal_allocation  # Added missing optimal strategy
        }
        # Handle unknown strategies gracefully
        if allocation_strategy not in self.strategies:
            logger.warning(f"Unknown allocation strategy '{allocation_strategy}', using 'balanced'")
            allocation_strategy = 'balanced'
        self.allocation_strategy = self.strategies[allocation_strategy]
        
    def select_venue(self, action: Dict) -> str:
        return self.allocation_strategy(action)
    
    def _balanced_allocation(self, action):
        # Implementation for balanced dark pool allocation
        return self.venues[0] if self.venues else "default"
    
    def _aggressive_allocation(self, action):
        # Implementation for aggressive dark pool allocation
        return self.venues[-1] if self.venues else "default"
    
    def _round_robin_allocation(self, action):
        # Implementation for round-robin dark pool allocation
        if not self.venues:
            return "default"
        if not hasattr(self, '_current_venue_index'):
            self._current_venue_index = 0
        venue = self.venues[self._current_venue_index]
        self._current_venue_index = (self._current_venue_index + 1) % len(self.venues)
        return venue

    def _optimal_allocation(self, action):
        # Implementation for optimal dark pool allocation
        # For now, use balanced allocation as optimal
        # In production, this would analyze liquidity, fees, and execution quality
        return self._balanced_allocation(action)

# FIXED: Removed Git merge conflict marker
class HybridTradingAgent:
    def __init__(self, config: Dict):
        self.config = config
        self._init_core_components()
        self._init_compliance_systems()
        self._init_risk_controls()
        self._init_arbitrage_engine()

        # FIXED: Dark pool configuration with defaults
        dark_pool_config = config.get('dark_pool', {
            'size_threshold': 100000,
            'venues': ['coinbase', 'bybit'],
            'allocation_strategy': 'optimal'
        })
        self.dark_pool_threshold = dark_pool_config.get('size_threshold', 100000)

        # Initialize DarkPoolRouter with proper parameters
        venues = dark_pool_config.get('venues', ['coinbase', 'bybit'])
        allocation_strategy = dark_pool_config.get('allocation_strategy', 'balanced')

        try:
            self.dark_pool_router = DarkPoolRouter(venues, allocation_strategy)
        except Exception as e:
            logger.warning(f"Failed to initialize DarkPoolRouter: {e}")
            self.dark_pool_router = None

        # Dark pool configuration
        self.dark_pool_threshold = dark_pool_config.get('size_threshold', 100000)

    def _init_core_components(self):
        """Initialize main trading components with graceful fallbacks"""
        try:
            # Try to load pre-trained LSTM model
            if os.path.exists("lstm_processor.pt"):
                self.lstm = torch.jit.load("lstm_processor.pt")
                logger.info("✅ [HYBRID] Loaded pre-trained LSTM model")
            else:
                # Create new LSTM model if pre-trained doesn't exist
                from .lstm_processor import LSTMProcessor
                self.lstm = LSTMProcessor(input_size=30, hidden_size=128, num_layers=3)
                logger.info("🔧 [HYBRID] Created new LSTM model (no pre-trained found)")
        except Exception as e:
            logger.warning(f"⚠️ [HYBRID] LSTM initialization failed: {e}")
            self.lstm = None

        try:
            self.som = SOMemory()
            logger.info("✅ [HYBRID] SOM memory initialized")
        except Exception as e:
            logger.warning(f"⚠️ [HYBRID] SOM memory initialization failed: {e}")
            self.som = None

        try:
            # Try to load pre-trained RL agent
            if os.path.exists("ppo_trading.zip"):
                self.rl_agent = PPO.load("ppo_trading.zip")
                logger.info("✅ [HYBRID] Loaded pre-trained RL agent")
            else:
                # Create new RL agent if pre-trained doesn't exist
                logger.info("🔧 [HYBRID] Creating new RL agent (no pre-trained found)")
                self.rl_agent = None  # Will be initialized when needed
        except Exception as e:
            logger.warning(f"⚠️ [HYBRID] RL agent initialization failed: {e}")
            self.rl_agent = None

        try:
            self.alpha_net = AlphaNet().eval()
            logger.info("✅ [HYBRID] AlphaNet initialized")
        except Exception as e:
            logger.warning(f"⚠️ [HYBRID] AlphaNet initialization failed: {e}")
            self.alpha_net = None

    def _init_compliance_systems(self):
        """MiFID II compliant systems"""
        # Get compliance config with safe defaults
        compliance_config = self.config.get('compliance', {})

        # Generate encryption key if not provided
        encryption_key = compliance_config.get('encryption_key')
        if not encryption_key:
            from cryptography.fernet import Fernet
            encryption_key = Fernet.generate_key().decode()
            logger.warning("[COMPLIANCE] No encryption key provided, generated new key")

        retention_days = compliance_config.get('retention_days', 7)

        self.audit_logger = MiFIDIILogger(
            retention_days=retention_days,
            encryption_key=encryption_key
        )
        self.audit_vault = AuditVault(
            cloud_storage=compliance_config.get('cloud_storage', True),
            immutable_logging=compliance_config.get('immutable_logging', True)
        )

    def _init_risk_controls(self):
        """Exchange circuit breakers and liquidity checks"""
        self.market_monitor = CircuitBreaker(
            volatility_threshold=0.15,
            price_drop_limit=0.05
        )
        self.liquidity_checker = LiquidityAnalyzer(
            config={
                'min_liquidity_multiplier': 1.5,
                'slippage_tolerance': 0.002
            }
        )

    def _init_arbitrage_engine(self):
        """Cross-venue arbitrage detection"""
        self.arbitrage_detector = CrossVenueArbitrageDetector(
            venues=['binance', 'coinbase', 'kraken'],
            min_spread=0.0005,
            fee_adjusted=True
        )

    async def execute_order(self, action: Dict) -> Dict:
        """Enhanced institutional order execution pipeline"""
        try:
            # 1. Check market circuit breakers
            if self.market_monitor.breach_detected():
                raise CircuitBreakerActivated("Market volatility too high")
            
            # 2. Check liquidity conditions
            liquidity_ok = await self.liquidity_checker.validate(
                action['symbol'],
                action['amount']
            )
            if not liquidity_ok:
                action = self._adjust_order_for_liquidity(action)
            
            # 3. Dark pool routing decision
            if action['amount'] >= self.dark_pool_threshold:
                execution_venue = self.dark_pool_router.select_venue(action)
                action['venue'] = execution_venue
                action['dark_pool'] = True
            else:
                action['venue'] = 'lit_pool'
                action['dark_pool'] = False
                
            # 4. Arbitrage check
            arbitrage_opp = await self.arbitrage_detector.find_opportunity(
                action['symbol']
            )
            if arbitrage_opp:
                action = self._merge_arbitrage_action(action, arbitrage_opp)
            
            # 5. Execute with compliance logging
            result = await self._safe_execute(action)
            
            # 6. Post-trade processing
            await self._post_trade_updates(action, result)
            return result
            
        except CircuitBreakerActivated as e:
            logger.critical(f"Market shutdown: {str(e)}")
            return {'status': 'failed', 'reason': 'circuit_breaker'}

    async def _safe_execute(self, action: Dict) -> Dict:
        """Audited order execution with compliance checks"""
        audit_record = {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'order_id': self._generate_order_id(action),
            'details': action,
            'pre_trade_checks': {
                'liquidity': self.liquidity_checker.last_check,
                'circuit_breaker': self.market_monitor.status()
            }
        }
        
        try:
            # Execute order through appropriate venue
            if action['dark_pool']:
                result = await self.dark_pool_router.execute(action)
            else:
                result = await self.lit_pool_executor.execute(action)
                
            # Log successful execution
            audit_record['result'] = result
            self.audit_logger.log(audit_record)
            self.audit_vault.store(audit_record)
            
            return result
            
        except Exception as e:
            audit_record['error'] = str(e)
            self.audit_logger.log(audit_record)
            self.audit_vault.store(audit_record)
            raise

    async def _post_trade_updates(self, action: Dict, result: Dict):
        """Update systems post-execution"""
        # Update market monitor with trade impact
        self.market_monitor.report_trade(
            symbol=action['symbol'],
            amount=action['amount'],
            price=result['price']
        )
        
        # Update liquidity model
        await self.liquidity_checker.update_liquidity_profile(
            action['symbol'],
            result['executed_qty']
        )
        
        # Refresh arbitrage opportunities
        await self.arbitrage_detector.refresh_data()

    def _adjust_order_for_liquidity(self, action: Dict) -> Dict:
        """Order slicing based on liquidity conditions"""
        max_size = self.liquidity_checker.get_max_order_size(action['symbol'])
        if max_size <= 0:
            raise InsufficientLiquidity("No available liquidity for execution")
            
        return {
            **action,
            'amount': min(action['amount'], max_size),
            'sliced': True,
            'original_amount': action['amount']
        }

    def _merge_arbitrage_action(self, action: Dict, arbitrage: Dict) -> Dict:
        """Combine base action with arbitrage opportunity"""
        return {
            **action,
            'arbitrage': True,
            'venue_pair': arbitrage['venues'],
            'spread': arbitrage['spread'],
            'target_price': arbitrage['target_price']
        }

    def _generate_order_id(self, action: Dict) -> str:
        """Generate MiFID II compliant order ID"""
        components = [
            action['symbol'],
            str(action['amount']),
            datetime.now(timezone.utc).isoformat(),
            action.get('venue', 'unknown')
        ]
        return hashlib.sha256('|'.join(components).encode()).hexdigest()

# ---------------
# Support Classes
# ---------------
class CircuitBreakerActivated(Exception):
    pass

class InsufficientLiquidity(Exception):
    pass

class DarkPoolRouter:
    def __init__(self, venues: List[str], allocation_strategy: str = 'balanced'):
        self.venues = venues
        self.strategies = {
            'balanced': self._balanced_allocation,
            'aggressive': self._aggressive_allocation
        }
        self.allocation_strategy = self.strategies[allocation_strategy]
        
    def select_venue(self, action: Dict) -> str:
        return self.allocation_strategy(action)
        
    def _balanced_allocation(self, action):
        """Balanced dark pool allocation strategy"""
        # Distribute orders evenly across available venues
        import random
        return random.choice(self.venues) if self.venues else None

    def _aggressive_allocation(self, action):
        """Aggressive allocation to highest liquidity venue"""
        # For now, return first venue (can be enhanced with liquidity data)
        return self.venues[0] if self.venues else None

    def _round_robin_allocation(self, action):
        """Round-robin allocation across venues"""
        if not hasattr(self, '_venue_index'):
            self._venue_index = 0
        venue = self.venues[self._venue_index % len(self.venues)] if self.venues else None
        self._venue_index += 1
        return venue

    def _optimal_allocation(self, action):
        """Optimal allocation based on current market conditions"""
        # Enhanced strategy that considers market conditions
        # For now, use balanced as fallback
        return self._balanced_allocation(action)

class CrossVenueArbitrageDetector:
    def __init__(self, venues: List[str], min_spread: float, fee_adjusted: bool):
        self.venues = venues
        self.min_spread = min_spread
        self.fee_adjusted = fee_adjusted
        
    async def find_opportunity(self, symbol: str) -> Optional[Dict]:
        """Find cross-venue arbitrage opportunities"""
        try:
            # Simulate arbitrage detection (would connect to real venues in production)
            import random

            # Mock price differences between venues
            if random.random() > 0.8:  # 20% chance of finding opportunity
                spread = random.uniform(self.min_spread, self.min_spread * 2)
                return {
                    'symbol': symbol,
                    'buy_venue': random.choice(self.venues),
                    'sell_venue': random.choice(self.venues),
                    'spread': spread,
                    'profit_potential': spread * 0.8 if self.fee_adjusted else spread
                }
            return None
        except Exception as e:
            logger.error(f"Error finding arbitrage opportunity: {e}")
            return None

class MiFIDIILogger:
    def __init__(self, retention_days: int, encryption_key: str):
        self.retention = retention_days
        self.cipher = Fernet(encryption_key)
        
    def log(self, record: Dict):
        """Log trading record with MiFID II compliance"""
        try:
            encrypted_record = self._encrypt_record(record)
            # Store encrypted record with timestamp
            timestamp = time.time()
            log_entry = {
                'timestamp': timestamp,
                'encrypted_data': encrypted_record.decode('utf-8', errors='ignore'),
                'record_id': f"mifid_{int(timestamp * 1000)}"
            }
            # In production, this would store to compliant database
            logger.debug(f"MiFID II record logged: {log_entry['record_id']}")
        except Exception as e:
            logger.error(f"Failed to log MiFID II record: {e}")
        
    def _encrypt_record(self, data: Dict) -> bytes:
        return self.cipher.encrypt(json.dumps(data).encode())

class AlphaNet:
    """Simple AlphaNet placeholder"""
    def eval(self):
        return self

class SOMemory:
    """Simple Self-Organizing Memory placeholder"""
    def __init__(self):
        logger.debug("[SOM] Self-organizing memory initialized")

class PPO:
    """Simple PPO placeholder"""
    @staticmethod
    def load(path: str):
        logger.debug(f"[PPO] Loading from {path}")
        return None

class CloudStorage:
    """Simple cloud storage placeholder"""
    def save(self, record: Dict, immutable: bool = False):
        # Placeholder for cloud storage
        logger.debug(f"[CLOUD-STORAGE] Saving record (immutable={immutable})")

class LocalStorage:
    """Simple local storage placeholder"""
    def save(self, record: Dict, immutable: bool = False):
        # Placeholder for local storage
        logger.debug(f"[LOCAL-STORAGE] Saving record (immutable={immutable})")

class AuditVault:
    def __init__(self, cloud_storage: bool, immutable_logging: bool):
        self.storage = CloudStorage() if cloud_storage else LocalStorage()
        self.immutable = immutable_logging

    def store(self, record: Dict):
        self.storage.save(record, immutable=self.immutable)