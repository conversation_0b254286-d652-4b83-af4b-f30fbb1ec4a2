#!/usr/bin/env python3
"""
Live Trading Validation Test
Validates that the comprehensive system can perform real trading operations
"""

import asyncio
import logging
import sys
import os
import time
import traceback
from pathlib import Path

# Add project root to path
PROJECT_ROOT = Path(__file__).parent
sys.path.insert(0, str(PROJECT_ROOT))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class LiveTradingValidationTest:
    """Validate live trading capabilities with strict performance targets"""
    
    def __init__(self):
        self.test_results = {}
        self.total_tests = 0
        self.passed_tests = 0
        self.failed_tests = 0
        self.performance_metrics = {}
        
    async def run_live_validation_tests(self):
        """Run comprehensive live trading validation tests"""
        logger.info("🚀 [LIVE-TEST] Starting Live Trading Validation Tests...")
        
        # Test 1: System Initialization Performance
        await self.test_system_initialization_performance()
        
        # Test 2: Exchange Connectivity Validation
        await self.test_exchange_connectivity()
        
        # Test 3: Real-Time Data Feed Validation
        await self.test_real_time_data_feeds()
        
        # Test 4: Trading Engine Activation
        await self.test_trading_engine_activation()
        
        # Test 5: Neural Component Performance
        await self.test_neural_component_performance()
        
        # Test 6: Persistent Learning Validation
        await self.test_persistent_learning()
        
        # Test 7: Performance Target Validation
        await self.test_performance_targets()
        
        # Test 8: Real Money Trading Validation
        await self.test_real_money_trading_validation()
        
        # Generate final validation report
        self.generate_validation_report()
        
        return self.passed_tests == self.total_tests
    
    async def test_system_initialization_performance(self):
        """Test system initialization meets performance targets"""
        logger.info("⚡ [LIVE-TEST] Testing system initialization performance...")
        
        self.total_tests += 1
        try:
            start_time = time.time()
            
            # Import and initialize the comprehensive system
            from main import ComprehensiveLiveTradingSystem
            trading_system = ComprehensiveLiveTradingSystem()
            
            # Test initialization time
            init_start = time.time()
            await trading_system.initialize_comprehensive_system()
            init_time = time.time() - init_start
            
            total_time = time.time() - start_time
            
            # Performance targets
            max_init_time = 120  # 2 minutes maximum
            
            self.performance_metrics['initialization_time'] = init_time
            self.performance_metrics['total_startup_time'] = total_time
            
            if init_time <= max_init_time:
                self.test_results["initialization_performance"] = "PASS"
                self.passed_tests += 1
                logger.info(f"✅ [LIVE-TEST] Initialization performance: PASS ({init_time:.2f}s)")
            else:
                self.test_results["initialization_performance"] = f"FAIL: Too slow ({init_time:.2f}s > {max_init_time}s)"
                self.failed_tests += 1
                logger.error(f"❌ [LIVE-TEST] Initialization performance: FAIL - Too slow")
            
            # Store system for other tests
            self.trading_system = trading_system
            
        except Exception as e:
            self.test_results["initialization_performance"] = f"FAIL: {e}"
            self.failed_tests += 1
            logger.error(f"❌ [LIVE-TEST] Initialization performance: FAIL - {e}")
    
    async def test_exchange_connectivity(self):
        """Test exchange connectivity and API response times"""
        logger.info("🔗 [LIVE-TEST] Testing exchange connectivity...")
        
        self.total_tests += 1
        try:
            if not hasattr(self, 'trading_system'):
                raise RuntimeError("System not initialized")
            
            # Test Bybit connectivity
            bybit_connected = False
            bybit_response_time = 0
            
            if 'bybit' in self.trading_system.exchange_clients:
                start_time = time.time()
                try:
                    bybit_client = self.trading_system.exchange_clients['bybit']
                    # Test basic connectivity
                    if hasattr(bybit_client, 'get_server_time'):
                        await bybit_client.get_server_time()
                    bybit_response_time = time.time() - start_time
                    bybit_connected = True
                except Exception as e:
                    logger.warning(f"⚠️ [LIVE-TEST] Bybit connectivity issue: {e}")
            
            # Performance target: <500ms for trading APIs
            max_response_time = 0.5
            
            self.performance_metrics['bybit_response_time'] = bybit_response_time
            
            if bybit_connected and bybit_response_time <= max_response_time:
                self.test_results["exchange_connectivity"] = "PASS"
                self.passed_tests += 1
                logger.info(f"✅ [LIVE-TEST] Exchange connectivity: PASS ({bybit_response_time:.3f}s)")
            else:
                self.test_results["exchange_connectivity"] = f"FAIL: Response time {bybit_response_time:.3f}s"
                self.failed_tests += 1
                logger.error(f"❌ [LIVE-TEST] Exchange connectivity: FAIL")
            
        except Exception as e:
            self.test_results["exchange_connectivity"] = f"FAIL: {e}"
            self.failed_tests += 1
            logger.error(f"❌ [LIVE-TEST] Exchange connectivity: FAIL - {e}")
    
    async def test_real_time_data_feeds(self):
        """Test real-time data feed performance"""
        logger.info("📊 [LIVE-TEST] Testing real-time data feeds...")
        
        self.total_tests += 1
        try:
            if not hasattr(self, 'trading_system'):
                raise RuntimeError("System not initialized")
            
            # Test data feed latency
            data_feeds_active = len(self.trading_system.data_feeds)
            
            # Performance target: <1000ms for data feeds
            max_data_latency = 1.0
            
            if data_feeds_active >= 5:  # Minimum expected data feeds
                self.test_results["real_time_data_feeds"] = "PASS"
                self.passed_tests += 1
                logger.info(f"✅ [LIVE-TEST] Real-time data feeds: PASS ({data_feeds_active} feeds)")
            else:
                self.test_results["real_time_data_feeds"] = f"FAIL: Insufficient feeds ({data_feeds_active})"
                self.failed_tests += 1
                logger.error(f"❌ [LIVE-TEST] Real-time data feeds: FAIL")
            
        except Exception as e:
            self.test_results["real_time_data_feeds"] = f"FAIL: {e}"
            self.failed_tests += 1
            logger.error(f"❌ [LIVE-TEST] Real-time data feeds: FAIL - {e}")
    
    async def test_trading_engine_activation(self):
        """Test all trading engines are active and finding opportunities"""
        logger.info("⚡ [LIVE-TEST] Testing trading engine activation...")
        
        self.total_tests += 1
        try:
            if not hasattr(self, 'trading_system'):
                raise RuntimeError("System not initialized")
            
            # Test trading engines
            engines_active = len(self.trading_system.trading_engines)
            
            # Minimum expected engines
            min_engines = 5
            
            if engines_active >= min_engines:
                self.test_results["trading_engine_activation"] = "PASS"
                self.passed_tests += 1
                logger.info(f"✅ [LIVE-TEST] Trading engine activation: PASS ({engines_active} engines)")
            else:
                self.test_results["trading_engine_activation"] = f"FAIL: Insufficient engines ({engines_active})"
                self.failed_tests += 1
                logger.error(f"❌ [LIVE-TEST] Trading engine activation: FAIL")
            
        except Exception as e:
            self.test_results["trading_engine_activation"] = f"FAIL: {e}"
            self.failed_tests += 1
            logger.error(f"❌ [LIVE-TEST] Trading engine activation: FAIL - {e}")
    
    async def test_neural_component_performance(self):
        """Test neural component performance targets"""
        logger.info("🧠 [LIVE-TEST] Testing neural component performance...")
        
        self.total_tests += 1
        try:
            if not hasattr(self, 'trading_system'):
                raise RuntimeError("System not initialized")
            
            # Test neural components
            neural_components_active = len(self.trading_system.neural_components)
            
            # Performance target: <200ms for neural inference
            max_neural_latency = 0.2
            
            # Minimum expected neural components
            min_neural_components = 10
            
            if neural_components_active >= min_neural_components:
                self.test_results["neural_component_performance"] = "PASS"
                self.passed_tests += 1
                logger.info(f"✅ [LIVE-TEST] Neural component performance: PASS ({neural_components_active} components)")
            else:
                self.test_results["neural_component_performance"] = f"FAIL: Insufficient components ({neural_components_active})"
                self.failed_tests += 1
                logger.error(f"❌ [LIVE-TEST] Neural component performance: FAIL")
            
        except Exception as e:
            self.test_results["neural_component_performance"] = f"FAIL: {e}"
            self.failed_tests += 1
            logger.error(f"❌ [LIVE-TEST] Neural component performance: FAIL - {e}")
    
    async def test_persistent_learning(self):
        """Test persistent learning system"""
        logger.info("🎓 [LIVE-TEST] Testing persistent learning system...")
        
        self.total_tests += 1
        try:
            if not hasattr(self, 'trading_system'):
                raise RuntimeError("System not initialized")
            
            # Test persistent learning
            learning_systems_active = len(self.trading_system.learning_systems)
            
            if learning_systems_active >= 1 and self.trading_system.persistent_learning:
                self.test_results["persistent_learning"] = "PASS"
                self.passed_tests += 1
                logger.info(f"✅ [LIVE-TEST] Persistent learning: PASS ({learning_systems_active} systems)")
            else:
                self.test_results["persistent_learning"] = f"FAIL: Learning system not active"
                self.failed_tests += 1
                logger.error(f"❌ [LIVE-TEST] Persistent learning: FAIL")
            
        except Exception as e:
            self.test_results["persistent_learning"] = f"FAIL: {e}"
            self.failed_tests += 1
            logger.error(f"❌ [LIVE-TEST] Persistent learning: FAIL - {e}")
    
    async def test_performance_targets(self):
        """Test all performance targets are met"""
        logger.info("🚀 [LIVE-TEST] Testing performance targets...")
        
        self.total_tests += 1
        try:
            # Check all performance metrics
            targets_met = 0
            total_targets = 0
            
            # API response time target
            if 'bybit_response_time' in self.performance_metrics:
                total_targets += 1
                if self.performance_metrics['bybit_response_time'] <= 0.5:
                    targets_met += 1
            
            # Initialization time target
            if 'initialization_time' in self.performance_metrics:
                total_targets += 1
                if self.performance_metrics['initialization_time'] <= 120:
                    targets_met += 1
            
            if targets_met == total_targets and total_targets > 0:
                self.test_results["performance_targets"] = "PASS"
                self.passed_tests += 1
                logger.info(f"✅ [LIVE-TEST] Performance targets: PASS ({targets_met}/{total_targets})")
            else:
                self.test_results["performance_targets"] = f"FAIL: {targets_met}/{total_targets} targets met"
                self.failed_tests += 1
                logger.error(f"❌ [LIVE-TEST] Performance targets: FAIL")
            
        except Exception as e:
            self.test_results["performance_targets"] = f"FAIL: {e}"
            self.failed_tests += 1
            logger.error(f"❌ [LIVE-TEST] Performance targets: FAIL - {e}")
    
    async def test_real_money_trading_validation(self):
        """Test real money trading validation systems"""
        logger.info("💰 [LIVE-TEST] Testing real money trading validation...")
        
        self.total_tests += 1
        try:
            if not hasattr(self, 'trading_system'):
                raise RuntimeError("System not initialized")
            
            # Test real money validation systems
            validation_systems = 0
            
            if 'real_money_enforcer' in self.trading_system.monitoring_systems:
                validation_systems += 1
            
            if 'real_money_validator' in self.trading_system.monitoring_systems:
                validation_systems += 1
            
            if validation_systems >= 1:
                self.test_results["real_money_trading_validation"] = "PASS"
                self.passed_tests += 1
                logger.info(f"✅ [LIVE-TEST] Real money trading validation: PASS ({validation_systems} systems)")
            else:
                self.test_results["real_money_trading_validation"] = f"FAIL: No validation systems"
                self.failed_tests += 1
                logger.error(f"❌ [LIVE-TEST] Real money trading validation: FAIL")
            
        except Exception as e:
            self.test_results["real_money_trading_validation"] = f"FAIL: {e}"
            self.failed_tests += 1
            logger.error(f"❌ [LIVE-TEST] Real money trading validation: FAIL - {e}")
    
    def generate_validation_report(self):
        """Generate comprehensive validation report"""
        logger.info("📊 [LIVE-TEST] Generating live trading validation report...")
        
        success_rate = (self.passed_tests / self.total_tests * 100) if self.total_tests > 0 else 0
        
        print("\n" + "="*80)
        print("🎯 LIVE TRADING VALIDATION REPORT")
        print("="*80)
        print(f"📊 Total Tests: {self.total_tests}")
        print(f"✅ Passed: {self.passed_tests}")
        print(f"❌ Failed: {self.failed_tests}")
        print(f"📈 Success Rate: {success_rate:.1f}%")
        print("="*80)
        
        # Performance metrics
        if self.performance_metrics:
            print("\n🚀 PERFORMANCE METRICS:")
            for metric, value in self.performance_metrics.items():
                if isinstance(value, float):
                    print(f"  • {metric}: {value:.3f}s")
                else:
                    print(f"  • {metric}: {value}")
        
        if self.failed_tests > 0:
            print("\n❌ FAILED TESTS:")
            for test_name, result in self.test_results.items():
                if result.startswith("FAIL"):
                    print(f"  • {test_name}: {result}")
        
        if success_rate >= 95:
            print("\n🎉 EXCELLENT: System is ready for live trading!")
        elif success_rate >= 85:
            print("\n✅ GOOD: System is mostly ready for live trading!")
        elif success_rate >= 70:
            print("\n⚠️ WARNING: Some issues need to be addressed!")
        else:
            print("\n❌ CRITICAL: System is not ready for live trading!")
        
        print("="*80)

async def main():
    """Run live trading validation tests"""
    try:
        test_system = LiveTradingValidationTest()
        success = await test_system.run_live_validation_tests()
        
        if success:
            logger.info("🎉 [SUCCESS] ALL live trading validation tests passed!")
            return 0
        else:
            logger.error("❌ [FAILURE] Some validation tests failed!")
            return 1
            
    except Exception as e:
        logger.error(f"❌ [CRITICAL] Validation test execution failed: {e}")
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
