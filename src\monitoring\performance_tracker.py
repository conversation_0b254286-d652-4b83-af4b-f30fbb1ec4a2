"""
Professional-grade performance tracking and analytics system
"""
import time
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from collections import defaultdict, deque
from decimal import Decimal
import statistics

logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetrics:
    """Performance metrics data structure"""
    timestamp: float
    total_profit: Decimal
    total_trades: int
    win_rate: float
    avg_trade_duration: float
    profit_velocity: Decimal  # Profit per minute
    active_positions: int
    available_balance: Decimal
    total_equity: Decimal
    drawdown: float
    sharpe_ratio: float

@dataclass
class StrategyPerformance:
    """Strategy-specific performance tracking"""
    strategy_name: str
    total_profit: Decimal = Decimal("0")
    total_trades: int = 0
    winning_trades: int = 0
    losing_trades: int = 0
    avg_profit_per_trade: Decimal = Decimal("0")
    max_profit: Decimal = Decimal("0")
    max_loss: Decimal = Decimal("0")
    avg_duration: float = 0.0
    last_trade_time: float = 0.0
    confidence_score: float = 0.0

class PerformanceTracker:
    """Enterprise-grade performance tracking system"""
    
    def __init__(self):
        self.metrics_history: deque = deque(maxlen=1000)
        self.strategy_performance: Dict[str, StrategyPerformance] = {}
        self.trade_history: deque = deque(maxlen=500)
        self.profit_timeline: deque = deque(maxlen=1000)
        self.start_time = time.time()
        self.initial_balance = Decimal("0")
        self.peak_equity = Decimal("0")
        self.max_drawdown = 0.0
        
        # Performance targets
        self.targets = {
            'daily_profit_target': Decimal("10.0"),  # $10 per day
            'win_rate_target': 60.0,  # 60% win rate
            'max_drawdown_limit': 15.0,  # 15% max drawdown
            'min_trades_per_hour': 2,  # Minimum activity level
            'profit_velocity_target': Decimal("0.1")  # $0.10 per minute
        }
        
        logger.info("📈 [PERFORMANCE-TRACKER] Professional performance tracking initialized")
    
    def record_trade(self, symbol: str, strategy: str, profit_loss: Decimal, 
                    duration_minutes: float, confidence: float = 0.0):
        """Record a completed trade"""
        try:
            current_time = time.time()
            
            # Record trade
            trade_record = {
                'timestamp': current_time,
                'symbol': symbol,
                'strategy': strategy,
                'profit_loss': profit_loss,
                'duration_minutes': duration_minutes,
                'confidence': confidence
            }
            self.trade_history.append(trade_record)
            
            # Update strategy performance
            if strategy not in self.strategy_performance:
                self.strategy_performance[strategy] = StrategyPerformance(strategy_name=strategy)
            
            perf = self.strategy_performance[strategy]
            perf.total_trades += 1
            perf.total_profit += profit_loss
            perf.last_trade_time = current_time
            
            if profit_loss > 0:
                perf.winning_trades += 1
                perf.max_profit = max(perf.max_profit, profit_loss)
            else:
                perf.losing_trades += 1
                perf.max_loss = min(perf.max_loss, profit_loss)
            
            # Update averages
            perf.avg_profit_per_trade = perf.total_profit / perf.total_trades
            perf.avg_duration = (perf.avg_duration * (perf.total_trades - 1) + duration_minutes) / perf.total_trades
            perf.confidence_score = (perf.confidence_score * 0.9 + confidence * 0.1)
            
            # Add to profit timeline
            self.profit_timeline.append({
                'timestamp': current_time,
                'profit': float(profit_loss),
                'cumulative': float(sum(p['profit'] for p in self.profit_timeline) + profit_loss)
            })
            
            logger.debug(f"📊 [TRADE-RECORDED] {symbol} {strategy}: ${profit_loss:.4f} in {duration_minutes:.1f}min")
            
        except Exception as e:
            logger.error(f"Error recording trade: {e}")
    
    def update_portfolio_metrics(self, available_balance: Decimal, total_equity: Decimal, 
                                active_positions: int):
        """Update current portfolio metrics"""
        try:
            current_time = time.time()
            
            # Set initial balance if not set
            if self.initial_balance == Decimal("0"):
                self.initial_balance = total_equity
            
            # Update peak equity and drawdown
            if total_equity > self.peak_equity:
                self.peak_equity = total_equity
            
            current_drawdown = float((self.peak_equity - total_equity) / self.peak_equity * 100) if self.peak_equity > 0 else 0.0
            self.max_drawdown = max(self.max_drawdown, current_drawdown)
            
            # Calculate metrics
            total_profit = total_equity - self.initial_balance
            total_trades = sum(len(self.trade_history))
            
            # Calculate win rate
            if self.trade_history:
                winning_trades = sum(1 for trade in self.trade_history if trade['profit_loss'] > 0)
                win_rate = (winning_trades / len(self.trade_history)) * 100
            else:
                win_rate = 0.0
            
            # Calculate average trade duration
            if self.trade_history:
                avg_duration = statistics.mean(trade['duration_minutes'] for trade in self.trade_history)
            else:
                avg_duration = 0.0
            
            # Calculate profit velocity
            elapsed_minutes = (current_time - self.start_time) / 60
            profit_velocity = total_profit / Decimal(str(elapsed_minutes)) if elapsed_minutes > 0 else Decimal("0")
            
            # Calculate Sharpe ratio (simplified)
            sharpe_ratio = self._calculate_sharpe_ratio()
            
            # Create metrics record
            metrics = PerformanceMetrics(
                timestamp=current_time,
                total_profit=total_profit,
                total_trades=total_trades,
                win_rate=win_rate,
                avg_trade_duration=avg_duration,
                profit_velocity=profit_velocity,
                active_positions=active_positions,
                available_balance=available_balance,
                total_equity=total_equity,
                drawdown=current_drawdown,
                sharpe_ratio=sharpe_ratio
            )
            
            self.metrics_history.append(metrics)
            
        except Exception as e:
            logger.error(f"Error updating portfolio metrics: {e}")
    
    def _calculate_sharpe_ratio(self) -> float:
        """Calculate Sharpe ratio from recent trades"""
        try:
            if len(self.trade_history) < 10:
                return 0.0
            
            returns = [float(trade['profit_loss']) for trade in list(self.trade_history)[-30:]]
            
            if not returns or statistics.stdev(returns) == 0:
                return 0.0
            
            mean_return = statistics.mean(returns)
            std_return = statistics.stdev(returns)
            
            return mean_return / std_return
            
        except Exception:
            return 0.0
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive performance summary"""
        try:
            if not self.metrics_history:
                return {"status": "no_data", "message": "No performance data available"}
            
            latest = self.metrics_history[-1]
            
            # Calculate target achievement
            target_achievement = {
                'profit_target': float(latest.total_profit / self.targets['daily_profit_target'] * 100),
                'win_rate_target': latest.win_rate / self.targets['win_rate_target'] * 100,
                'drawdown_status': 'good' if latest.drawdown < self.targets['max_drawdown_limit'] else 'warning',
                'velocity_target': float(latest.profit_velocity / self.targets['profit_velocity_target'] * 100)
            }
            
            # Get top performing strategies
            top_strategies = sorted(
                self.strategy_performance.values(),
                key=lambda x: x.total_profit,
                reverse=True
            )[:5]
            
            return {
                'current_metrics': {
                    'total_profit': float(latest.total_profit),
                    'total_trades': latest.total_trades,
                    'win_rate': latest.win_rate,
                    'profit_velocity': float(latest.profit_velocity),
                    'drawdown': latest.drawdown,
                    'sharpe_ratio': latest.sharpe_ratio,
                    'active_positions': latest.active_positions,
                    'available_balance': float(latest.available_balance),
                    'total_equity': float(latest.total_equity)
                },
                'target_achievement': target_achievement,
                'top_strategies': [
                    {
                        'name': s.strategy_name,
                        'profit': float(s.total_profit),
                        'trades': s.total_trades,
                        'win_rate': (s.winning_trades / s.total_trades * 100) if s.total_trades > 0 else 0,
                        'avg_profit': float(s.avg_profit_per_trade),
                        'confidence': s.confidence_score
                    }
                    for s in top_strategies
                ],
                'uptime_hours': (time.time() - self.start_time) / 3600,
                'performance_grade': self._calculate_performance_grade(latest)
            }
            
        except Exception as e:
            logger.error(f"Error generating performance summary: {e}")
            return {"status": "error", "message": str(e)}
    
    def _calculate_performance_grade(self, metrics: PerformanceMetrics) -> str:
        """Calculate overall performance grade"""
        try:
            score = 0
            
            # Profit score (40%)
            if metrics.total_profit > 0:
                score += 40
            elif metrics.total_profit > -5:
                score += 20
            
            # Win rate score (30%)
            if metrics.win_rate >= 60:
                score += 30
            elif metrics.win_rate >= 50:
                score += 20
            elif metrics.win_rate >= 40:
                score += 10
            
            # Drawdown score (20%)
            if metrics.drawdown < 5:
                score += 20
            elif metrics.drawdown < 10:
                score += 15
            elif metrics.drawdown < 15:
                score += 10
            
            # Activity score (10%)
            if metrics.total_trades > 10:
                score += 10
            elif metrics.total_trades > 5:
                score += 5
            
            # Assign grade
            if score >= 85:
                return "A"
            elif score >= 70:
                return "B"
            elif score >= 55:
                return "C"
            elif score >= 40:
                return "D"
            else:
                return "F"
                
        except Exception:
            return "N/A"
    
    def get_strategy_comparison(self) -> Dict[str, Any]:
        """Get detailed strategy performance comparison"""
        try:
            comparison = {}
            
            for strategy, perf in self.strategy_performance.items():
                win_rate = (perf.winning_trades / perf.total_trades * 100) if perf.total_trades > 0 else 0
                
                comparison[strategy] = {
                    'total_profit': float(perf.total_profit),
                    'total_trades': perf.total_trades,
                    'win_rate': win_rate,
                    'avg_profit_per_trade': float(perf.avg_profit_per_trade),
                    'max_profit': float(perf.max_profit),
                    'max_loss': float(perf.max_loss),
                    'avg_duration': perf.avg_duration,
                    'confidence_score': perf.confidence_score,
                    'last_active': perf.last_trade_time,
                    'profit_per_minute': float(perf.total_profit / (perf.avg_duration * perf.total_trades)) if perf.total_trades > 0 and perf.avg_duration > 0 else 0
                }
            
            return comparison
            
        except Exception as e:
            logger.error(f"Error generating strategy comparison: {e}")
            return {}

# Global performance tracker instance
performance_tracker = PerformanceTracker()
