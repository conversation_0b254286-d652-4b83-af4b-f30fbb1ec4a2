"""
Risk limits and constraints for trading operations
"""

import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from decimal import Decimal
from datetime import datetime

logger = logging.getLogger(__name__)

@dataclass
class PositionLimits:
    """Position size limits for risk management"""
    max_position_size: Decimal
    max_position_value: Decimal
    max_daily_trades: int
    max_concentration: float  # Maximum % of portfolio in single asset

@dataclass
class ExposureLimits:
    """Exposure limits across different dimensions"""
    max_total_exposure: Decimal
    max_currency_exposure: Dict[str, Decimal]
    max_exchange_exposure: Dict[str, Decimal]
    max_sector_exposure: Dict[str, float]

@dataclass
class PortfolioLimits:
    """Portfolio-level limits for risk management"""
    max_total_exposure: Decimal
    max_leverage: Decimal
    max_drawdown: Decimal
    max_var: Decimal

@dataclass
class TradingLimits:
    """Trading operation limits"""
    max_orders_per_minute: int
    max_order_value: Decimal
    min_order_value: Decimal
    max_slippage: Decimal

@dataclass
class RiskLimits:
    """Comprehensive risk limits for trading system"""
    max_var: float  # Maximum Value at Risk
    max_cvar: float  # Maximum Conditional VaR
    max_drawdown: float  # Maximum allowed drawdown
    min_sharpe_ratio: float  # Minimum Sharpe ratio
    max_volatility: float  # Maximum volatility

    position_limits: PositionLimits
    exposure_limits: ExposureLimits
    
    def __post_init__(self):
        """Validate limits after initialization"""
        if self.max_var <= 0:
            self.max_var = 0.05  # 5% default
        if self.max_cvar <= 0:
            self.max_cvar = 0.08  # 8% default
        if self.max_drawdown <= 0:
            self.max_drawdown = 0.15  # 15% default

class RiskLimitChecker:
    """Check trading operations against risk limits"""
    
    def __init__(self, limits: RiskLimits):
        self.limits = limits
        self.current_positions = {}
        self.daily_trades = 0
        self.last_reset_date = datetime.now().date()
    
    def check_position_limit(self, symbol: str, new_position_size: Decimal, 
                           current_price: float) -> bool:
        """Check if new position would exceed limits"""
        try:
            # Reset daily counter if new day
            current_date = datetime.now().date()
            if current_date != self.last_reset_date:
                self.daily_trades = 0
                self.last_reset_date = current_date
            
            # Check daily trade limit
            if self.daily_trades >= self.limits.position_limits.max_daily_trades:
                logger.warning(f"Daily trade limit exceeded: {self.daily_trades}")
                return False
            
            # Check position size limit
            if abs(new_position_size) > self.limits.position_limits.max_position_size:
                logger.warning(f"Position size limit exceeded: {new_position_size}")
                return False
            
            # Check position value limit
            position_value = abs(new_position_size) * Decimal(str(current_price))
            if position_value > self.limits.position_limits.max_position_value:
                logger.warning(f"Position value limit exceeded: {position_value}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking position limit: {e}")
            return False
    
    def check_exposure_limit(self, currency: str, additional_exposure: Decimal) -> bool:
        """Check if additional exposure would exceed currency limits"""
        try:
            current_exposure = self.limits.exposure_limits.max_currency_exposure.get(
                currency, Decimal('0')
            )
            
            max_allowed = self.limits.exposure_limits.max_currency_exposure.get(
                currency, Decimal('1000000')  # Default large limit
            )
            
            if current_exposure + additional_exposure > max_allowed:
                logger.warning(f"Currency exposure limit exceeded for {currency}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking exposure limit: {e}")
            return False
    
    def check_risk_metrics(self, var: float, cvar: float, drawdown: float, 
                          volatility: float) -> bool:
        """Check if risk metrics are within acceptable limits"""
        try:
            if var > self.limits.max_var:
                logger.warning(f"VaR limit exceeded: {var} > {self.limits.max_var}")
                return False
            
            if cvar > self.limits.max_cvar:
                logger.warning(f"CVaR limit exceeded: {cvar} > {self.limits.max_cvar}")
                return False
            
            if drawdown > self.limits.max_drawdown:
                logger.warning(f"Drawdown limit exceeded: {drawdown} > {self.limits.max_drawdown}")
                return False
            
            if volatility > self.limits.max_volatility:
                logger.warning(f"Volatility limit exceeded: {volatility} > {self.limits.max_volatility}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking risk metrics: {e}")
            return False
    
    def update_position(self, symbol: str, position_size: Decimal):
        """Update current position tracking"""
        self.current_positions[symbol] = position_size
        self.daily_trades += 1
    
    def get_current_exposure(self) -> Dict[str, Decimal]:
        """Get current exposure by currency"""
        exposure = {}
        for symbol, position in self.current_positions.items():
            # Extract currency from symbol (simplified)
            currency = symbol.replace('USDT', '').replace('USD', '')
            if currency not in exposure:
                exposure[currency] = Decimal('0')
            exposure[currency] += abs(position)
        
        return exposure


class RiskLimitManager:
    """Comprehensive risk limit management system"""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)

        # Initialize default limits
        self.position_limits = PositionLimits(
            max_position_size=Decimal(self.config.get('max_position_size', '1000')),
            max_position_value=Decimal(self.config.get('max_position_value', '10000')),
            max_daily_trades=self.config.get('max_daily_trades', 100),
            max_concentration=self.config.get('max_concentration', 0.20)
        )

        self.portfolio_limits = PortfolioLimits(
            max_total_exposure=Decimal(self.config.get('max_total_exposure', '50000')),
            max_leverage=Decimal(self.config.get('max_leverage', '3')),
            max_drawdown=Decimal(self.config.get('max_drawdown', '0.15')),
            max_var=Decimal(self.config.get('max_var', '0.05'))
        )

        self.trading_limits = TradingLimits(
            max_orders_per_minute=self.config.get('max_orders_per_minute', 10),
            max_order_value=Decimal(self.config.get('max_order_value', '5000')),
            min_order_value=Decimal(self.config.get('min_order_value', '10')),
            max_slippage=Decimal(self.config.get('max_slippage', '0.01'))
        )

        # Risk monitoring
        self.current_positions = {}
        self.daily_trade_count = 0
        self.last_reset_date = datetime.now().date()

    def check_position_limits(self, symbol: str, quantity: Decimal, price: Decimal) -> Dict[str, Any]:
        """Check if a position violates position limits"""
        try:
            position_value = abs(quantity * price)
            violations = []

            # Check position size limit
            if abs(quantity) > self.position_limits.max_position_size:
                violations.append(f"Position size exceeds limit: {quantity} > {self.position_limits.max_position_size}")

            # Check position value limit
            if position_value > self.position_limits.max_position_value:
                violations.append(f"Position value exceeds limit: {position_value} > {self.position_limits.max_position_value}")

            # Check daily trade limit
            if self.daily_trade_count >= self.position_limits.max_daily_trades:
                violations.append(f"Daily trade limit exceeded: {self.daily_trade_count} >= {self.position_limits.max_daily_trades}")

            return {
                'allowed': len(violations) == 0,
                'violations': violations,
                'position_value': position_value,
                'daily_trades': self.daily_trade_count
            }

        except Exception as e:
            self.logger.error(f"Error checking position limits: {e}")
            return {'allowed': False, 'violations': [f"Error: {e}"]}

    def check_portfolio_limits(self, portfolio_value: Decimal, total_exposure: Decimal) -> Dict[str, Any]:
        """Check if portfolio violates portfolio limits"""
        try:
            violations = []

            # Check total exposure limit
            if total_exposure > self.portfolio_limits.max_total_exposure:
                violations.append(f"Total exposure exceeds limit: {total_exposure} > {self.portfolio_limits.max_total_exposure}")

            # Check leverage limit
            if portfolio_value > 0:
                leverage = total_exposure / portfolio_value
                if leverage > self.portfolio_limits.max_leverage:
                    violations.append(f"Leverage exceeds limit: {leverage} > {self.portfolio_limits.max_leverage}")

            return {
                'allowed': len(violations) == 0,
                'violations': violations,
                'total_exposure': total_exposure,
                'leverage': leverage if portfolio_value > 0 else Decimal('0')
            }

        except Exception as e:
            self.logger.error(f"Error checking portfolio limits: {e}")
            return {'allowed': False, 'violations': [f"Error: {e}"]}

    def check_trading_limits(self, order_value: Decimal, recent_orders: int) -> Dict[str, Any]:
        """Check if trading violates trading limits"""
        try:
            violations = []

            # Check order value limits
            if order_value > self.trading_limits.max_order_value:
                violations.append(f"Order value exceeds limit: {order_value} > {self.trading_limits.max_order_value}")

            if order_value < self.trading_limits.min_order_value:
                violations.append(f"Order value below minimum: {order_value} < {self.trading_limits.min_order_value}")

            # Check order frequency limit
            if recent_orders > self.trading_limits.max_orders_per_minute:
                violations.append(f"Order frequency exceeds limit: {recent_orders} > {self.trading_limits.max_orders_per_minute}")

            return {
                'allowed': len(violations) == 0,
                'violations': violations,
                'order_value': order_value,
                'recent_orders': recent_orders
            }

        except Exception as e:
            self.logger.error(f"Error checking trading limits: {e}")
            return {'allowed': False, 'violations': [f"Error: {e}"]}

    def update_position(self, symbol: str, quantity: Decimal, price: Decimal):
        """Update position tracking"""
        try:
            self.current_positions[symbol] = {
                'quantity': quantity,
                'price': price,
                'value': quantity * price,
                'timestamp': datetime.now()
            }

            # Reset daily counter if new day
            current_date = datetime.now().date()
            if current_date != self.last_reset_date:
                self.daily_trade_count = 0
                self.last_reset_date = current_date

            self.daily_trade_count += 1

        except Exception as e:
            self.logger.error(f"Error updating position: {e}")

    def get_risk_summary(self) -> Dict[str, Any]:
        """Get comprehensive risk summary"""
        try:
            total_value = sum(pos['value'] for pos in self.current_positions.values())
            total_exposure = sum(abs(pos['value']) for pos in self.current_positions.values())

            return {
                'position_count': len(self.current_positions),
                'total_portfolio_value': total_value,
                'total_exposure': total_exposure,
                'daily_trades': self.daily_trade_count,
                'limits': {
                    'position': {
                        'max_size': self.position_limits.max_position_size,
                        'max_value': self.position_limits.max_position_value,
                        'max_daily_trades': self.position_limits.max_daily_trades
                    },
                    'portfolio': {
                        'max_exposure': self.portfolio_limits.max_total_exposure,
                        'max_leverage': self.portfolio_limits.max_leverage,
                        'max_drawdown': self.portfolio_limits.max_drawdown
                    },
                    'trading': {
                        'max_orders_per_minute': self.trading_limits.max_orders_per_minute,
                        'max_order_value': self.trading_limits.max_order_value,
                        'min_order_value': self.trading_limits.min_order_value
                    }
                },
                'last_updated': datetime.now()
            }

        except Exception as e:
            self.logger.error(f"Error getting risk summary: {e}")
            return {'error': str(e)}

    def enforce_limits(self, symbol: str, quantity: Decimal, price: Decimal,
                      portfolio_value: Decimal = None, recent_orders: int = 0) -> Dict[str, Any]:
        """Comprehensive limit enforcement"""
        try:
            # Check all limits
            position_check = self.check_position_limits(symbol, quantity, price)

            if portfolio_value:
                total_exposure = sum(abs(pos['value']) for pos in self.current_positions.values())
                total_exposure += abs(quantity * price)  # Add proposed position
                portfolio_check = self.check_portfolio_limits(portfolio_value, total_exposure)
            else:
                portfolio_check = {'allowed': True, 'violations': []}

            order_value = abs(quantity * price)
            trading_check = self.check_trading_limits(order_value, recent_orders)

            # Combine results
            all_violations = []
            all_violations.extend(position_check.get('violations', []))
            all_violations.extend(portfolio_check.get('violations', []))
            all_violations.extend(trading_check.get('violations', []))

            allowed = (position_check.get('allowed', False) and
                      portfolio_check.get('allowed', False) and
                      trading_check.get('allowed', False))

            return {
                'allowed': allowed,
                'violations': all_violations,
                'checks': {
                    'position': position_check,
                    'portfolio': portfolio_check,
                    'trading': trading_check
                }
            }

        except Exception as e:
            self.logger.error(f"Error enforcing limits: {e}")
            return {'allowed': False, 'violations': [f"Error: {e}"]}
