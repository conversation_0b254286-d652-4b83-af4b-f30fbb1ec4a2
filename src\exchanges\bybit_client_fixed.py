# Enterprise-grade Bybit client with professional-tier timestamp synchronization and trading infrastructure
from pybit.unified_trading import HTTP
import logging
import time
import asyncio
import threading
import statistics
import decimal
from datetime import datetime
from decimal import Decimal
from typing import Optional, Dict, List, Tuple
from dataclasses import dataclass, field
from collections import deque, defaultdict
import requests
import json

# Import trade logger
try:
    from src.logging.trade_logger import trade_logger
except ImportError:
    # Fallback if trade logger not available
    trade_logger = None
    logging.warning("Trade logger not available - trade logging will be disabled")

# Import performance optimizer
try:
    from src.performance.trading_optimizer import performance_optimizer
except ImportError:
    # Fallback if performance optimizer not available
    performance_optimizer = None

# OPTIMIZED: Import high-speed performance components
try:
    from src.performance.speed_optimizer import fast_api_call, fast_balance_validation, cached_market_data, speed_optimizer
    from src.performance.intelligent_cache_manager import smart_cache
    from src.exchanges.high_speed_connection_pool import connection_pool
    PERFORMANCE_OPTIMIZATIONS_AVAILABLE = True
    logging.info("🚀 [BYBIT] High-speed performance optimizations loaded")
except ImportError as e:
    PERFORMANCE_OPTIMIZATIONS_AVAILABLE = False
    logging.warning(f"⚠️ [BYBIT] Performance optimizations not available: {e}")
    # Create fallback decorators
    def fast_api_call(func):
        return func
    def fast_balance_validation(func):
        return func
    def cached_market_data(ttl_seconds=None):
        def decorator(func):
            return func
        return decorator
    def smart_cache(category=None, ttl=None, priority=None):
        def decorator(func):
            return func
        return decorator
    logging.warning("Performance optimizer not available - performance optimization will be disabled")

logger = logging.getLogger(__name__)


@dataclass
class TimestampSample:
    """Professional-grade timestamp sample for statistical analysis"""
    local_time: float
    server_time: float
    network_latency: float
    round_trip_time: float
    timestamp: float = field(default_factory=time.time)


@dataclass
class SynchronizationMetrics:
    """Comprehensive synchronization performance metrics"""
    offset_samples: deque = field(default_factory=lambda: deque(maxlen=50))
    latency_samples: deque = field(default_factory=lambda: deque(maxlen=50))
    success_rate: float = 0.0
    last_sync_timestamp: float = 0.0
    sync_count: int = 0
    error_count: int = 0


class TimestampSynchronizationManager:
    """Enterprise-grade timestamp synchronization with advanced statistical algorithms"""

    def __init__(self):
        self.metrics = SynchronizationMetrics()
        self.current_offset = 0.0
        self.confidence_threshold = 0.70  # FIXED: More lenient confidence threshold
        self.max_offset_deviation = 2000  # FIXED: Allow larger deviation (2 seconds)
        self.sync_lock = threading.RLock()
        self.last_successful_sync = 0.0
        self.sync_interval = 300  # 5 minutes
        self.emergency_sync_threshold = 5000  # 5 seconds

    def calculate_statistical_offset(self) -> Tuple[float, float]:
        """Calculate statistically robust offset using advanced algorithms"""
        if len(self.metrics.offset_samples) < 3:
            return 0.0, 0.0

        # Remove outliers using IQR method
        offsets = list(self.metrics.offset_samples)
        q1 = statistics.quantiles(offsets, n=4)[0]
        q3 = statistics.quantiles(offsets, n=4)[2]
        iqr = q3 - q1
        lower_bound = q1 - 1.5 * iqr
        upper_bound = q3 + 1.5 * iqr

        filtered_offsets = [x for x in offsets if lower_bound <= x <= upper_bound]

        if not filtered_offsets:
            filtered_offsets = offsets

        # Calculate weighted average based on network latency
        if len(self.metrics.latency_samples) == len(filtered_offsets):
            weights = [1.0 / (lat + 1) for lat in list(self.metrics.latency_samples)[-len(filtered_offsets):]]
            weighted_offset = sum(o * w for o, w in zip(filtered_offsets, weights)) / sum(weights)
        else:
            weighted_offset = statistics.median(filtered_offsets)

        confidence = min(len(filtered_offsets) / 10.0, 1.0)
        return weighted_offset, confidence

    def perform_synchronization(self) -> bool:
        """Execute professional-grade timestamp synchronization"""
        with self.sync_lock:
            try:
                samples = []
                successful_samples = 0

                # Collect multiple samples for statistical robustness
                for attempt in range(5):
                    try:
                        sample = self._collect_timestamp_sample()
                        if sample:
                            samples.append(sample)
                            successful_samples += 1
                    except Exception as e:
                        logger.warning(f"[TIMESTAMP-SYNC] Sample {attempt + 1} failed: {e}")
                        continue

                if successful_samples < 2:
                    logger.error("[TIMESTAMP-SYNC] Insufficient samples for reliable synchronization")
                    self.metrics.error_count += 1
                    return False

                # Calculate network-adjusted offsets
                for sample in samples:
                    # Compensate for network latency
                    adjusted_server_time = sample.server_time + (sample.network_latency / 2)
                    offset = adjusted_server_time - sample.local_time

                    self.metrics.offset_samples.append(offset)
                    self.metrics.latency_samples.append(sample.network_latency)

                # Calculate statistically robust offset
                new_offset, confidence = self.calculate_statistical_offset()

                if confidence >= self.confidence_threshold:
                    # Validate offset is reasonable
                    if abs(new_offset - self.current_offset) > self.max_offset_deviation:
                        logger.warning(f"[TIMESTAMP-SYNC] Large offset change detected: {new_offset - self.current_offset:.0f}ms")

                    self.current_offset = new_offset
                    self.last_successful_sync = time.time()
                    self.metrics.sync_count += 1
                    self.metrics.last_sync_timestamp = time.time()

                    # Update success rate
                    total_attempts = self.metrics.sync_count + self.metrics.error_count
                    self.metrics.success_rate = self.metrics.sync_count / total_attempts if total_attempts > 0 else 0.0

                    logger.info(f"[TIMESTAMP-SYNC] Synchronization successful: offset={new_offset:.0f}ms, confidence={confidence:.2f}")
                    return True
                else:
                    logger.warning(f"[TIMESTAMP-SYNC] Low confidence synchronization: {confidence:.2f}")
                    self.metrics.error_count += 1
                    return False

            except Exception as e:
                logger.error(f"[TIMESTAMP-SYNC] Synchronization failed: {e}")
                self.metrics.error_count += 1
                return False

    def _collect_timestamp_sample(self) -> Optional[TimestampSample]:
        """Collect high-precision timestamp sample"""
        try:
            start_time = time.time() * 1000

            response = requests.get(
                'https://api.bybit.com/v5/market/time',
                timeout=10,
                headers={'User-Agent': 'Professional-Trading-Client/1.0'}
            )

            end_time = time.time() * 1000

            if response.status_code == 200:
                data = response.json()
                if data.get('retCode') == 0:
                    server_time = int(data['result']['timeSecond']) * 1000
                    round_trip_time = end_time - start_time
                    network_latency = round_trip_time / 2

                    return TimestampSample(
                        local_time=start_time,
                        server_time=server_time,
                        network_latency=network_latency,
                        round_trip_time=round_trip_time
                    )

            return None

        except Exception as e:
            logger.debug(f"[TIMESTAMP-SYNC] Sample collection failed: {e}")
            return None

    def get_synchronized_timestamp(self) -> int:
        """Get enterprise-grade synchronized timestamp"""
        current_time = time.time()

        # Check if emergency re-sync is needed
        if (current_time - self.last_successful_sync > self.sync_interval or
            abs(self.current_offset) > self.emergency_sync_threshold):
            logger.info("[TIMESTAMP-SYNC] Triggering emergency synchronization")
            self.perform_synchronization()

        synchronized_time = (time.time() * 1000) + self.current_offset
        return int(synchronized_time)

    def is_synchronized(self) -> bool:
        """Check if synchronization is current and reliable"""
        return (time.time() - self.last_successful_sync < self.sync_interval and
                self.metrics.success_rate > 0.8)


class APIRequestManager:
    """Professional-grade API request management with advanced retry mechanisms"""

    def __init__(self, timestamp_manager: TimestampSynchronizationManager):
        self.timestamp_manager = timestamp_manager
        self.max_retries = 5
        self.base_delay = 0.5
        self.max_delay = 10.0
        self.backoff_multiplier = 2.0
        self.request_lock = threading.RLock()

    def execute_with_retry(self, api_call, *args, **kwargs):
        """Execute API call with sophisticated retry logic and timestamp management"""
        last_exception = None

        for attempt in range(self.max_retries):
            try:
                with self.request_lock:
                    # Ensure timestamp synchronization
                    if not self.timestamp_manager.is_synchronized():
                        logger.info(f"[API-REQUEST] Synchronizing timestamps before attempt {attempt + 1}")
                        self.timestamp_manager.perform_synchronization()

                    # Execute the API call
                    result = api_call(*args, **kwargs)

                    # Check for timestamp-related errors
                    if self._is_timestamp_error(result):
                        logger.warning(f"[API-REQUEST] Timestamp error on attempt {attempt + 1}: {result}")

                        if attempt < self.max_retries - 1:
                            # Force re-synchronization
                            self.timestamp_manager.perform_synchronization()

                            # Calculate exponential backoff delay
                            delay = min(self.base_delay * (self.backoff_multiplier ** attempt), self.max_delay)
                            time.sleep(delay)
                            continue

                    return result

            except Exception as e:
                last_exception = e
                logger.warning(f"[API-REQUEST] Attempt {attempt + 1} failed: {e}")

                if attempt < self.max_retries - 1:
                    delay = min(self.base_delay * (self.backoff_multiplier ** attempt), self.max_delay)
                    time.sleep(delay)
                    continue

        # All retries exhausted
        logger.error(f"[API-REQUEST] All {self.max_retries} attempts failed")
        if last_exception:
            raise last_exception
        return None

    def _is_timestamp_error(self, response) -> bool:
        """Detect timestamp-related errors with comprehensive pattern matching"""
        if not response or not isinstance(response, dict):
            return False

        ret_code = response.get('retCode')
        ret_msg = response.get('retMsg', '').lower()

        # Comprehensive timestamp error detection
        timestamp_error_codes = [10002, -1]
        timestamp_keywords = ['timestamp', 'recv window', 'time window', 'expired']

        return (ret_code in timestamp_error_codes or
                any(keyword in ret_msg for keyword in timestamp_keywords))


class DynamicMarketDiscovery:
    """Enterprise-grade dynamic market discovery system"""

    def __init__(self, client_session):
        self.session = client_session
        self.discovered_instruments = {}
        self.last_discovery_time = 0
        self.discovery_cache_duration = 3600  # 1 hour cache

    def get_all_instruments(self, category: str = "spot") -> List[Dict]:
        """Get all available trading instruments from Bybit"""
        try:
            logger.info(f"[MARKET-DISCOVERY] Fetching all {category} instruments...")

            response = self.session.get_instruments_info(category=category)

            if response and response.get("retCode") == 0:
                instruments = response.get("result", {}).get("list", [])
                logger.info(f"[MARKET-DISCOVERY] Found {len(instruments)} {category} instruments")
                return instruments
            else:
                logger.error(f"[MARKET-DISCOVERY] Failed to fetch instruments: {response}")
                return []

        except Exception as e:
            logger.error(f"[MARKET-DISCOVERY] Error fetching instruments: {e}")
            return []

    def discover_available_currencies(self) -> List[str]:
        """Discover all available currencies from trading pairs"""
        try:
            instruments = self.get_all_instruments("spot")
            currencies = set()

            for instrument in instruments:
                symbol = instrument.get("symbol", "")
                base_coin = instrument.get("baseCoin", "")
                quote_coin = instrument.get("quoteCoin", "")

                if base_coin:
                    currencies.add(base_coin)
                if quote_coin:
                    currencies.add(quote_coin)

                # Also extract from symbol if coins not provided
                if not base_coin or not quote_coin:
                    # Use existing extraction logic as fallback
                    extracted_base, extracted_quote = self._extract_currencies_from_symbol(symbol)
                    currencies.add(extracted_base)
                    currencies.add(extracted_quote)

            discovered_currencies = sorted(list(currencies))
            logger.info(f"[MARKET-DISCOVERY] Discovered {len(discovered_currencies)} currencies: {discovered_currencies[:10]}...")

            return discovered_currencies

        except Exception as e:
            logger.error(f"[MARKET-DISCOVERY] Error discovering currencies: {e}")
            return []

    def discover_trading_pairs_for_currency(self, currency: str) -> List[str]:
        """Discover all trading pairs available for a specific currency"""
        try:
            instruments = self.get_all_instruments("spot")
            pairs = []

            for instrument in instruments:
                symbol = instrument.get("symbol", "")
                base_coin = instrument.get("baseCoin", "")
                quote_coin = instrument.get("quoteCoin", "")
                status = instrument.get("status", "")

                # Only include active trading pairs
                if status.lower() == "trading":
                    if base_coin == currency or quote_coin == currency:
                        pairs.append(symbol)

            logger.info(f"[MARKET-DISCOVERY] Found {len(pairs)} trading pairs for {currency}")
            return pairs

        except Exception as e:
            logger.error(f"[MARKET-DISCOVERY] Error discovering pairs for {currency}: {e}")
            return []

    def get_instrument_info(self, symbol: str) -> Dict:
        """Get detailed information about a specific trading instrument"""
        try:
            instruments = self.get_all_instruments("spot")

            for instrument in instruments:
                if instrument.get("symbol") == symbol:
                    return instrument

            logger.warning(f"[MARKET-DISCOVERY] Instrument {symbol} not found")
            return {}

        except Exception as e:
            logger.error(f"[MARKET-DISCOVERY] Error getting instrument info for {symbol}: {e}")
            return {}

    def _extract_currencies_from_symbol(self, symbol: str) -> Tuple[str, str]:
        """Fallback method to extract currencies from symbol"""
        symbol = symbol.upper()

        # Common quote currency patterns (order matters - longer first)
        quote_currencies = ['USDT', 'USD', 'EUR', 'BTC', 'ETH', 'BNB', 'SOL']

        for quote in quote_currencies:
            if symbol.endswith(quote):
                base = symbol[:-len(quote)]
                if base:
                    return base, quote

        # Fallback
        if len(symbol) >= 6:
            return symbol[:-4], symbol[-4:]
        elif len(symbol) >= 5:
            return symbol[:-3], symbol[-3:]

        return symbol, 'USDT'


class AdaptiveBalanceManager:
    """Enterprise-grade adaptive balance management system"""

    def __init__(self):
        # Position sizing configuration
        self.default_position_size_percent = 22.5  # 22.5% of available balance
        self.min_position_size_percent = 5.0       # Minimum 5%
        self.max_position_size_percent = 30.0      # Maximum 30%

        # Risk management thresholds
        self.high_balance_threshold = 1000.0       # USD equivalent
        self.medium_balance_threshold = 100.0      # USD equivalent
        self.low_balance_threshold = 20.0          # USD equivalent

        # Dynamic sizing factors
        self.balance_size_multipliers = {
            'high': 1.2,    # Increase position size for high balances
            'medium': 1.0,  # Standard position size
            'low': 0.8      # Reduce position size for low balances
        }

        # Currency-specific risk factors
        self.currency_risk_factors = {
            'USDT': 0.95,  # Very low risk (stablecoin)
            'USD': 0.95,   # Very low risk (fiat)
            'EUR': 0.95,   # Very low risk (fiat)
            'BTC': 1.0,    # Standard risk (major crypto)
            'ETH': 1.0,    # Standard risk (major crypto)
            'SOL': 1.1,    # Slightly higher risk
            'ADA': 1.1,    # Slightly higher risk
            'DOT': 1.1,    # Slightly higher risk
            'BNB': 1.05,   # Slightly higher risk
            'default': 1.2 # Higher risk for unknown currencies
        }

    def calculate_adaptive_position_size(self, available_balance: float, currency: str,
                                       total_portfolio_value_usd: float = 0) -> Dict:
        """Calculate adaptive position size based on balance and risk factors"""
        try:
            # Determine balance category
            balance_category = self._categorize_balance(available_balance, currency)

            # Get base position size percentage
            base_percentage = self.default_position_size_percent

            # Apply balance-based multiplier
            balance_multiplier = self.balance_size_multipliers.get(balance_category, 1.0)

            # Apply currency-specific risk factor
            currency_risk = self.currency_risk_factors.get(currency,
                                                         self.currency_risk_factors['default'])

            # Calculate adjusted percentage
            adjusted_percentage = base_percentage * balance_multiplier / currency_risk

            # Ensure within bounds
            adjusted_percentage = max(self.min_position_size_percent,
                                    min(self.max_position_size_percent, adjusted_percentage))

            # Calculate position size
            position_size = available_balance * (adjusted_percentage / 100.0)

            return {
                'position_size': position_size,
                'position_percentage': adjusted_percentage,
                'available_balance': available_balance,
                'currency': currency,
                'balance_category': balance_category,
                'balance_multiplier': balance_multiplier,
                'currency_risk_factor': currency_risk,
                'base_percentage': base_percentage,
                'adaptive_factors': {
                    'balance_based': balance_multiplier,
                    'risk_based': 1.0 / currency_risk,
                    'final_adjustment': adjusted_percentage / base_percentage
                }
            }

        except Exception as e:
            logger.error(f"Error calculating adaptive position size: {e}")
            # Fallback to conservative sizing
            fallback_size = available_balance * 0.1  # 10% fallback
            return {
                'position_size': fallback_size,
                'position_percentage': 10.0,
                'available_balance': available_balance,
                'currency': currency,
                'error': str(e),
                'fallback': True
            }

    def _categorize_balance(self, balance: float, currency: str) -> str:
        """Categorize balance as high, medium, or low based on USD equivalent"""
        try:
            # Convert to approximate USD value for categorization
            # This is a simplified conversion - in production would use real exchange rates
            usd_equivalent = self._estimate_usd_value(balance, currency)

            if usd_equivalent >= self.high_balance_threshold:
                return 'high'
            elif usd_equivalent >= self.medium_balance_threshold:
                return 'medium'
            else:
                return 'low'

        except Exception:
            return 'medium'  # Default to medium if conversion fails

    def _estimate_usd_value(self, balance: float, currency: str) -> float:
        """Estimate USD value of balance (simplified approximation)"""
        # Simplified conversion rates for categorization
        # In production, this would use real-time exchange rates
        approximate_rates = {
            'USDT': 1.0,
            'USD': 1.0,
            'EUR': 1.1,
            'BTC': 100000.0,  # Approximate BTC price
            'ETH': 4000.0,    # Approximate ETH price
            'SOL': 200.0,     # Approximate SOL price
            'ADA': 0.5,       # Approximate ADA price
            'DOT': 8.0,       # Approximate DOT price
            'BNB': 600.0,     # Approximate BNB price
        }

        rate = approximate_rates.get(currency, 1.0)  # Default to 1:1 if unknown
        return balance * rate

    def get_optimal_position_sizes_for_portfolio(self, all_balances: Dict[str, float]) -> Dict:
        """Calculate optimal position sizes for entire portfolio"""
        try:
            portfolio_analysis = {}
            total_portfolio_usd = 0

            # Calculate total portfolio value
            for currency, balance in all_balances.items():
                usd_value = self._estimate_usd_value(balance, currency)
                total_portfolio_usd += usd_value

            # Calculate adaptive position sizes for each currency
            for currency, balance in all_balances.items():
                if balance > 0:
                    position_info = self.calculate_adaptive_position_size(
                        balance, currency, total_portfolio_usd
                    )
                    portfolio_analysis[currency] = position_info

            # Add portfolio summary
            portfolio_analysis['_portfolio_summary'] = {
                'total_currencies': len(all_balances),
                'total_usd_value': total_portfolio_usd,
                'active_currencies': len([b for b in all_balances.values() if b > 0]),
                'recommended_total_exposure': sum(
                    info['position_size'] for info in portfolio_analysis.values()
                    if isinstance(info, dict) and 'position_size' in info
                )
            }

            return portfolio_analysis

        except Exception as e:
            logger.error(f"Error calculating portfolio position sizes: {e}")
            return {}


class MultiCurrencyManager:
    """Enterprise-grade multi-currency trading and balance management"""

    def __init__(self, client_session=None):
        # Initialize with dynamic market discovery
        self.market_discovery = DynamicMarketDiscovery(client_session) if client_session else None

        # Initialize adaptive balance manager
        self.balance_manager = AdaptiveBalanceManager()

        # Base supported currencies (will be expanded dynamically)
        self.base_supported_currencies = [
            'USDT', 'USD', 'EUR', 'BTC', 'ETH', 'SOL', 'BNB', 'ADA', 'DOT', 'XRP',
            'MATIC', 'AVAX', 'LINK', 'UNI', 'LTC', 'BCH', 'ETC', 'FIL', 'ATOM'
        ]

        # Will be populated dynamically
        self.supported_currencies = self.base_supported_currencies.copy()

        # Currency priority for trading (higher priority = preferred for trading)
        self.currency_priority = {
            'USDT': 100, 'USD': 95, 'EUR': 90,  # Stablecoins/fiat highest priority
            'BTC': 85, 'ETH': 80,               # Major cryptos high priority
            'SOL': 75, 'BNB': 70, 'ADA': 65,   # Top altcoins medium-high priority
            'DOT': 60, 'XRP': 55, 'MATIC': 50, # Other altcoins medium priority
            'AVAX': 45, 'LINK': 40, 'UNI': 35, # DeFi tokens lower priority
            'LTC': 30, 'BCH': 25, 'ETC': 20,   # Legacy coins low priority
            'FIL': 15, 'ATOM': 10              # Specialized tokens lowest priority
        }

        # Common trading pairs and their quote currencies
        self.pair_quote_currencies = {
            'USDT': ['USDT'],
            'USD': ['USD', 'USDT'],
            'EUR': ['EUR', 'USD', 'USDT'],
            'BTC': ['BTC', 'USDT', 'USD'],
            'ETH': ['ETH', 'BTC', 'USDT', 'USD']
        }

    def extract_base_quote_from_symbol(self, symbol: str) -> Tuple[str, str]:
        """Extract base and quote currencies from trading symbol"""
        symbol = symbol.upper()

        # Common quote currency patterns (order matters - longer first)
        quote_currencies = ['USDT', 'USD', 'EUR', 'BTC', 'ETH', 'BNB', 'SOL']

        for quote in quote_currencies:
            if symbol.endswith(quote):
                base = symbol[:-len(quote)]
                if base:  # Ensure base currency exists
                    return base, quote

        # Fallback: assume last 3-4 characters are quote currency
        if len(symbol) >= 6:
            base = symbol[:-4]
            quote = symbol[-4:]
            return base, quote
        elif len(symbol) >= 5:
            base = symbol[:-3]
            quote = symbol[-3:]
            return base, quote

        # Default fallback
        return symbol, 'USDT'

    def get_currency_priority(self, currency: str) -> int:
        """Get priority score for a currency (higher = better)"""
        return self.currency_priority.get(currency.upper(), 0)

    def sort_currencies_by_priority(self, currencies: List[str]) -> List[str]:
        """Sort currencies by trading priority (highest first)"""
        return sorted(currencies, key=self.get_currency_priority, reverse=True)

    def get_alternative_quote_currencies(self, primary_quote: str) -> List[str]:
        """Get alternative quote currencies for cross-currency trading"""
        alternatives = []

        # Add direct alternatives based on common pairs
        if primary_quote in self.pair_quote_currencies:
            alternatives.extend(self.pair_quote_currencies[primary_quote])

        # Add high-priority stable currencies as universal alternatives
        universal_alternatives = ['USDT', 'USD', 'EUR', 'BTC', 'ETH']
        for alt in universal_alternatives:
            if alt not in alternatives and alt != primary_quote:
                alternatives.append(alt)

        # Sort by priority
        return self.sort_currencies_by_priority(alternatives)

    def get_optimal_trading_route(self, from_currency: str, to_currency: str,
                                 available_balances: Dict[str, float]) -> Dict:
        """Find optimal trading route between currencies"""
        try:
            # Direct trading pair check
            direct_pairs = [
                f"{from_currency}{to_currency}",
                f"{to_currency}{from_currency}"
            ]

            # Check if we have market discovery available
            if self.market_discovery:
                instruments = self.market_discovery.get_all_instruments("spot")
                available_symbols = [inst.get("symbol", "") for inst in instruments]

                # Check for direct pair
                for pair in direct_pairs:
                    if pair in available_symbols:
                        return {
                            'route_type': 'direct',
                            'trading_pair': pair,
                            'steps': 1,
                            'estimated_cost': 0.001,  # Single trading fee
                            'path': [from_currency, to_currency]
                        }

                # Find intermediate currency route
                intermediate_currencies = ['USDT', 'BTC', 'ETH', 'USD', 'EUR']

                for intermediate in intermediate_currencies:
                    if intermediate == from_currency or intermediate == to_currency:
                        continue

                    # Check if both legs exist
                    leg1 = f"{from_currency}{intermediate}"
                    leg2 = f"{intermediate}{to_currency}"

                    if leg1 in available_symbols and leg2 in available_symbols:
                        return {
                            'route_type': 'intermediate',
                            'trading_pairs': [leg1, leg2],
                            'intermediate_currency': intermediate,
                            'steps': 2,
                            'estimated_cost': 0.002,  # Two trading fees
                            'path': [from_currency, intermediate, to_currency]
                        }

            # Fallback route
            return {
                'route_type': 'fallback',
                'trading_pair': f"{from_currency}USDT",
                'steps': 1,
                'estimated_cost': 0.001,
                'path': [from_currency, 'USDT']
            }

        except Exception as e:
            logger.error(f"Error finding optimal trading route: {e}")
            return {
                'route_type': 'error',
                'error': str(e)
            }

    def calculate_conversion_rate(self, from_currency: str, to_currency: str,
                                amount: float) -> Dict:
        """Calculate conversion rate between currencies"""
        try:
            if from_currency == to_currency:
                return {
                    'rate': 1.0,
                    'converted_amount': amount,
                    'fees': 0.0,
                    'route': 'same_currency'
                }

            # Get optimal route
            route = self.get_optimal_trading_route(from_currency, to_currency, {})

            if route['route_type'] == 'direct':
                # Direct conversion
                trading_pair = route['trading_pair']

                # Determine if we're buying or selling
                if trading_pair == f"{from_currency}{to_currency}":
                    # Selling from_currency for to_currency
                    # Need current price of the pair
                    conversion_rate = 1.0  # Placeholder - would get from price API
                else:
                    # Buying from_currency with to_currency
                    conversion_rate = 1.0  # Placeholder - would get from price API

                fees = amount * 0.001  # 0.1% trading fee
                converted_amount = (amount * conversion_rate) - fees

                return {
                    'rate': conversion_rate,
                    'converted_amount': converted_amount,
                    'fees': fees,
                    'route': 'direct',
                    'trading_pair': trading_pair
                }

            elif route['route_type'] == 'intermediate':
                # Two-step conversion
                intermediate = route['intermediate_currency']

                # Step 1: from_currency -> intermediate
                step1_fees = amount * 0.001
                intermediate_amount = amount - step1_fees  # Simplified

                # Step 2: intermediate -> to_currency
                step2_fees = intermediate_amount * 0.001
                final_amount = intermediate_amount - step2_fees  # Simplified

                total_fees = step1_fees + step2_fees

                return {
                    'rate': final_amount / amount,
                    'converted_amount': final_amount,
                    'fees': total_fees,
                    'route': 'intermediate',
                    'intermediate_currency': intermediate,
                    'trading_pairs': route['trading_pairs']
                }

            else:
                # Fallback or error
                return {
                    'rate': 0.95,  # Conservative estimate with 5% buffer
                    'converted_amount': amount * 0.95,
                    'fees': amount * 0.05,
                    'route': 'fallback'
                }

        except Exception as e:
            logger.error(f"Error calculating conversion rate: {e}")
            return {
                'rate': 0.9,  # Very conservative fallback
                'converted_amount': amount * 0.9,
                'fees': amount * 0.1,
                'route': 'error',
                'error': str(e)
            }


class UnifiedAccountManager:
    """Professional-grade Unified Trading Account management"""

    def __init__(self):
        self.supported_account_types = ["UNIFIED"]
        self.account_capabilities = {
            "UNIFIED": {
                "spot_trading": True,
                "derivatives_trading": True,
                "margin_trading": True,
                "cross_collateral": True
            }
        }

    def validate_account_type(self, account_type: str) -> bool:
        """Validate account type against current Bybit specifications"""
        return account_type in self.supported_account_types

    def get_primary_account_type(self) -> str:
        """Get the primary account type for all operations"""
        return "UNIFIED"

    def get_account_capabilities(self, account_type: Optional[str] = None) -> Dict:
        """Get comprehensive account capabilities"""
        account_type = account_type or self.get_primary_account_type()
        return self.account_capabilities.get(account_type, {})


class RealMoneyTradingVerifier:
    """Enterprise-grade real money trading verification system"""

    def __init__(self):
        # Verification flags
        self.real_money_mode_enabled = True
        self.simulation_mode_blocked = True
        self.balance_change_verification_enabled = True

        # Verification thresholds
        self.minimum_balance_change_threshold = 0.000001  # Minimum detectable balance change
        self.verification_timeout_seconds = 30  # Max time to wait for balance verification

        # Tracking
        self.pre_trade_balances = {}
        self.post_trade_balances = {}
        self.verified_trades = []
        self.failed_verifications = []

    def verify_real_money_mode(self, client_session) -> Dict:
        """Verify that we're in real money trading mode"""
        try:
            logger.info("[REAL-MONEY-VERIFY] Verifying real money trading mode...")

            verification_results = {
                'real_money_mode': False,
                'testnet_disabled': False,
                'api_permissions_verified': False,
                'balance_access_verified': False,
                'trading_enabled': False,
                'overall_verified': False,
                'errors': []
            }

            # Check 1: Verify not in testnet mode
            if hasattr(client_session, 'testnet') and not client_session.testnet:
                verification_results['testnet_disabled'] = True
                logger.info("[REAL-MONEY-VERIFY] ✓ Testnet mode disabled")
            else:
                verification_results['errors'].append("Testnet mode detected or unknown")
                logger.error("[REAL-MONEY-VERIFY] ✗ Testnet mode detected")

            # Check 2: Verify API permissions
            try:
                account_info = client_session.get_account_info()
                if account_info and account_info.get("retCode") == 0:
                    verification_results['api_permissions_verified'] = True
                    logger.info("[REAL-MONEY-VERIFY] ✓ API permissions verified")
                else:
                    verification_results['errors'].append("API permissions check failed")
                    logger.error("[REAL-MONEY-VERIFY] ✗ API permissions check failed")
            except Exception as e:
                verification_results['errors'].append(f"API permissions error: {e}")
                logger.error(f"[REAL-MONEY-VERIFY] ✗ API permissions error: {e}")

            # Check 3: Verify balance access
            try:
                wallet_balance = client_session.get_wallet_balance(accountType="UNIFIED")
                if wallet_balance and wallet_balance.get("retCode") == 0:
                    verification_results['balance_access_verified'] = True
                    logger.info("[REAL-MONEY-VERIFY] ✓ Balance access verified")
                else:
                    verification_results['errors'].append("Balance access check failed")
                    logger.error("[REAL-MONEY-VERIFY] ✗ Balance access check failed")
            except Exception as e:
                verification_results['errors'].append(f"Balance access error: {e}")
                logger.error(f"[REAL-MONEY-VERIFY] ✗ Balance access error: {e}")

            # Check 4: Verify trading capabilities
            try:
                # Test with a very small order that would be rejected due to minimum size
                # This verifies trading API access without actually placing a trade
                test_params = {
                    "category": "spot",
                    "symbol": "BTCUSDT",
                    "side": "Buy",
                    "orderType": "Market",
                    "qty": "0.000001",  # Intentionally too small
                    "timeInForce": "IOC"
                }

                # This should fail due to minimum order size, but confirms trading API access
                response = client_session.place_order(**test_params)

                # We expect this to fail with minimum order size error, which confirms trading access
                if response and "retCode" in response:
                    verification_results['trading_enabled'] = True
                    logger.info("[REAL-MONEY-VERIFY] ✓ Trading API access verified")
                else:
                    verification_results['errors'].append("Trading API access check failed")
                    logger.error("[REAL-MONEY-VERIFY] ✗ Trading API access check failed")

            except Exception as e:
                # Some errors are expected (like minimum order size), so we check the error type
                error_msg = str(e).lower()
                if any(keyword in error_msg for keyword in ['minimum', 'size', 'qty', 'amount']):
                    verification_results['trading_enabled'] = True
                    logger.info("[REAL-MONEY-VERIFY] ✓ Trading API access verified (minimum order error expected)")
                else:
                    verification_results['errors'].append(f"Trading API error: {e}")
                    logger.error(f"[REAL-MONEY-VERIFY] ✗ Trading API error: {e}")

            # Overall verification
            verification_results['real_money_mode'] = (
                verification_results['testnet_disabled'] and
                verification_results['api_permissions_verified'] and
                verification_results['balance_access_verified'] and
                verification_results['trading_enabled']
            )

            verification_results['overall_verified'] = verification_results['real_money_mode']

            if verification_results['overall_verified']:
                logger.info("[REAL-MONEY-VERIFY] ✅ REAL MONEY TRADING MODE VERIFIED!")
            else:
                logger.error("[REAL-MONEY-VERIFY] ❌ REAL MONEY TRADING VERIFICATION FAILED!")
                logger.error(f"[REAL-MONEY-VERIFY] Errors: {verification_results['errors']}")

            return verification_results

        except Exception as e:
            logger.error(f"[REAL-MONEY-VERIFY] Critical verification error: {e}")
            return {
                'real_money_mode': False,
                'overall_verified': False,
                'errors': [f"Critical verification error: {e}"]
            }

    def capture_pre_trade_balances(self, client, currencies: List[str]) -> Dict:
        """Capture balances before trade execution"""
        try:
            logger.info(f"[BALANCE-VERIFY] Capturing pre-trade balances for {currencies}")

            pre_balances = {}
            for currency in currencies:
                try:
                    balance = float(client.get_balance(currency))
                    pre_balances[currency] = balance
                    logger.info(f"[BALANCE-VERIFY] Pre-trade {currency}: {balance:.8f}")
                except Exception as e:
                    logger.warning(f"[BALANCE-VERIFY] Could not get {currency} balance: {e}")
                    pre_balances[currency] = 0.0

            self.pre_trade_balances = pre_balances
            return pre_balances

        except Exception as e:
            logger.error(f"[BALANCE-VERIFY] Error capturing pre-trade balances: {e}")
            return {}

    def capture_post_trade_balances(self, client, currencies: List[str]) -> Dict:
        """Capture balances after trade execution"""
        try:
            logger.info(f"[BALANCE-VERIFY] Capturing post-trade balances for {currencies}")

            post_balances = {}
            for currency in currencies:
                try:
                    balance = float(client.get_balance(currency))
                    post_balances[currency] = balance
                    logger.info(f"[BALANCE-VERIFY] Post-trade {currency}: {balance:.8f}")
                except Exception as e:
                    logger.warning(f"[BALANCE-VERIFY] Could not get {currency} balance: {e}")
                    post_balances[currency] = 0.0

            self.post_trade_balances = post_balances
            return post_balances

        except Exception as e:
            logger.error(f"[BALANCE-VERIFY] Error capturing post-trade balances: {e}")
            return {}

    def verify_balance_changes(self, expected_changes: Dict[str, float]) -> Dict:
        """Verify that balance changes match expected changes from trade execution"""
        try:
            logger.info("[BALANCE-VERIFY] Verifying balance changes...")

            verification_result = {
                'verified': False,
                'changes_detected': {},
                'expected_changes': expected_changes,
                'verification_errors': [],
                'balance_change_confirmed': False
            }

            # Calculate actual balance changes
            for currency in self.pre_trade_balances:
                pre_balance = self.pre_trade_balances.get(currency, 0.0)
                post_balance = self.post_trade_balances.get(currency, 0.0)
                actual_change = post_balance - pre_balance

                verification_result['changes_detected'][currency] = {
                    'pre_balance': pre_balance,
                    'post_balance': post_balance,
                    'actual_change': actual_change,
                    'expected_change': expected_changes.get(currency, 0.0)
                }

                logger.info(f"[BALANCE-VERIFY] {currency}: {pre_balance:.8f} -> {post_balance:.8f} "
                           f"(change: {actual_change:.8f}, expected: {expected_changes.get(currency, 0.0):.8f})")

            # Verify changes match expectations
            significant_changes = 0
            for currency, change_info in verification_result['changes_detected'].items():
                actual_change = change_info['actual_change']
                expected_change = change_info['expected_change']

                # Check if change is significant (above threshold)
                if abs(actual_change) >= self.minimum_balance_change_threshold:
                    significant_changes += 1

                    # Check if change matches expectation (within tolerance)
                    tolerance = max(abs(expected_change) * 0.01, self.minimum_balance_change_threshold)  # 1% tolerance
                    if abs(actual_change - expected_change) <= tolerance:
                        logger.info(f"[BALANCE-VERIFY] ✓ {currency} change verified: "
                                   f"{actual_change:.8f} ≈ {expected_change:.8f}")
                    else:
                        verification_result['verification_errors'].append(
                            f"{currency}: actual change {actual_change:.8f} != expected {expected_change:.8f}"
                        )
                        logger.warning(f"[BALANCE-VERIFY] ✗ {currency} change mismatch: "
                                      f"{actual_change:.8f} != {expected_change:.8f}")

            # Overall verification
            verification_result['balance_change_confirmed'] = significant_changes > 0
            verification_result['verified'] = (
                verification_result['balance_change_confirmed'] and
                len(verification_result['verification_errors']) == 0
            )

            if verification_result['verified']:
                logger.info("[BALANCE-VERIFY] ✅ BALANCE CHANGES VERIFIED - REAL MONEY TRADING CONFIRMED!")
                self.verified_trades.append({
                    'timestamp': time.time(),
                    'changes': verification_result['changes_detected'],
                    'verification_status': 'verified'
                })
            else:
                logger.error("[BALANCE-VERIFY] ❌ BALANCE VERIFICATION FAILED!")
                logger.error(f"[BALANCE-VERIFY] Errors: {verification_result['verification_errors']}")
                self.failed_verifications.append({
                    'timestamp': time.time(),
                    'changes': verification_result['changes_detected'],
                    'errors': verification_result['verification_errors'],
                    'verification_status': 'failed'
                })

            return verification_result

        except Exception as e:
            logger.error(f"[BALANCE-VERIFY] Error verifying balance changes: {e}")
            return {
                'verified': False,
                'error': str(e),
                'verification_status': 'error'
            }


class BybitClientFixed:
    def __init__(self, api_key: str, api_secret: str, testnet: bool = False):
        """Initialize Bybit client with API credentials for LIVE TRADING ONLY."""
        # API key and secret are assumed to be decrypted and passed directly
        # from main.py (sourced from CredentialDecryptor via os.getenv)
        if not api_key or not api_secret:
            logger.error("[ERROR] Bybit API key or secret is missing. Cannot initialize client.")
            raise ValueError("Bybit API key and secret are required.")

        logger.info(f"[AUTH] Initializing Bybit client with API key: {api_key[:8]}... (length: {len(api_key)})")

        # Force testnet=False for LIVE TRADING ONLY
        if testnet:
            logger.warning("[WARNING] testnet=True was passed but forcing testnet=False for LIVE TRADING")

        # Enterprise-grade timestamp synchronization infrastructure
        self.timestamp_sync_manager = TimestampSynchronizationManager()
        self.api_request_manager = APIRequestManager(self.timestamp_sync_manager)
        self.account_manager = UnifiedAccountManager()
        # Currency manager will be initialized after session

        # Advanced configuration parameters
        self.recv_window = 20000     # Extended receive window for stability (20 seconds)
        self.max_retries = 5         # Enhanced retry mechanism
        self.request_timeout = 30    # Extended timeout for reliability

        try:
            # Initialize session with custom recv_window
            self.session = HTTP(
                api_key=api_key,
                api_secret=api_secret,
                testnet=False,  # LIVE TRADING ONLY
                recv_window=self.recv_window
            )
            self.name = "bybit_fixed"
            self.api_key = api_key
            self.api_secret = api_secret

            # OPTIMIZED: Initialize performance optimization components
            if PERFORMANCE_OPTIMIZATIONS_AVAILABLE:
                self.base_url = "https://api.bybit.com"
                self._price_cache = {}
                self._balance_cache = {}
                self._cache_ttl = 3.0  # 3 second cache TTL for high-speed trading
                logger.info("🚀 [BYBIT] Performance optimizations initialized")
            else:
                logger.warning("⚠️ [BYBIT] Performance optimizations not available")
            self.is_authenticated = False

            # Initialize currency manager now that session is available
            self.currency_manager = MultiCurrencyManager(self.session)

            # Initialize real money trading verification
            self.trading_verifier = RealMoneyTradingVerifier()

            # Initialize rate limiting
            self.rate_limiter = defaultdict(deque)
            self.rate_limits = {
                'default': {'requests': 120, 'window': 60},  # 120 requests per minute
                'order': {'requests': 50, 'window': 60},     # 50 orders per minute
                'balance': {'requests': 60, 'window': 60},   # 60 balance checks per minute
                'market_data': {'requests': 200, 'window': 60}  # 200 market data requests per minute
            }

            logger.info("[AUTH] Bybit client initialized successfully with rate limiting.")

            # Initialize enterprise-grade timestamp synchronization (non-blocking)
            logger.info("[ENTERPRISE-INIT] Performing initial timestamp synchronization...")
            try:
                sync_success = self.timestamp_sync_manager.perform_synchronization()
                if sync_success:
                    logger.info("[ENTERPRISE-INIT] ✅ Initial synchronization successful")
                else:
                    logger.info("[ENTERPRISE-INIT] ⚠️ Initial synchronization skipped - will sync during operations")
            except Exception as e:
                logger.info(f"[ENTERPRISE-INIT] ⚠️ Initial synchronization failed: {e} - will sync during operations")

            # Test credentials with enhanced diagnostics
            logger.info("[AUTH] Testing Bybit API connection...")
            self._test_connection_with_diagnostics()

            # PERFORMANCE OPTIMIZATION: Preload precision cache for instant access
            logger.info("[ENTERPRISE-INIT] Preloading precision cache for optimal performance...")
            self.preload_precision_cache()

            # Perform real money trading verification
            logger.info("[ENTERPRISE-INIT] Performing real money trading verification...")
            verification_result = self.trading_verifier.verify_real_money_mode(self.session)
            if verification_result['overall_verified']:
                logger.info("[ENTERPRISE-INIT] ✅ Real money trading mode verified!")
            else:
                logger.error("[ENTERPRISE-INIT] ❌ Real money trading verification failed!")
                logger.error(f"[ENTERPRISE-INIT] Verification errors: {verification_result['errors']}")
                # Continue but log the failure - user requirements specify no simulation fallbacks

        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize Bybit client: {e}")
            self.session = None
            self.is_authenticated = False
    
    def _test_connection_with_diagnostics(self):
        """Test connection with comprehensive error diagnostics"""
        try:
            # Try multiple endpoints to verify authentication
            test_methods = [
                ("get_account_info", "Account Info"),
                ("get_wallet_balance", "Wallet Balance"),
                ("get_instruments_info", "Instruments Info")
            ]
            
            for method_name, description in test_methods:
                try:
                    if hasattr(self.session, method_name):
                        method = getattr(self.session, method_name)
                        
                        # Call method with appropriate parameters
                        if method_name == "get_wallet_balance":
                            response = method(accountType="UNIFIED")
                        elif method_name == "get_instruments_info":
                            response = method(category="spot", limit=1)
                        else:
                            response = method()
                        
                        if response and response.get("retCode") == 0:
                            logger.info(f"[OK] Bybit {description} test successful.")
                            self.is_authenticated = True
                            return
                        else:
                            error_code = response.get('retCode', 'Unknown')
                            error_msg = response.get('retMsg', 'Unknown error')
                            logger.warning(f"[WARNING] Bybit {description} test failed: {error_msg} (Code: {error_code})")
                            
                            # Provide specific error guidance
                            if error_code == 10003:
                                logger.error("[ERROR] Invalid API key - Please check:")
                                logger.error("1. API key is correctly decrypted")
                                logger.error("2. API key has not expired")
                                logger.error("3. API key has proper permissions for SPOT trading")
                                logger.error("4. IP whitelist includes your current IP")
                            elif error_code == 10004:
                                logger.error("[ERROR] Invalid signature - API secret may be incorrect")
                            elif error_code == 10005:
                                logger.error("[ERROR] Permission denied - API key lacks required permissions")
                    
                except Exception as method_error:
                    logger.warning(f"[WARNING] Error testing {description}: {method_error}")
                    continue
            
            # If we get here, all tests failed
            logger.warning("[WARNING] All Bybit connection tests failed")
            logger.info("[INFO] API Key format check:")
            logger.info(f"- Length: {len(self.api_key)} characters")
            logger.info(f"- Starts with: {self.api_key[:4]}...")
            logger.info(f"- Secret length: {len(self.api_secret)} characters")
            self.is_authenticated = False
            
        except Exception as conn_e:
            logger.warning(f"[WARNING] Failed to test Bybit connection during initialization: {conn_e}")
            logger.warning("[WARNING] Bybit client will be available but authentication status is unknown")
            self.is_authenticated = False
            # Don't raise - let the system continue without Bybit

    def _execute_with_enterprise_retry(self, api_call, *args, **kwargs):
        """Execute API call with enterprise-grade retry and timestamp management"""
        return self.api_request_manager.execute_with_retry(api_call, *args, **kwargs)

    def _get_synchronized_timestamp(self) -> int:
        """Get enterprise-grade synchronized timestamp"""
        return self.timestamp_sync_manager.get_synchronized_timestamp()

    async def get_all_available_balances(self) -> Dict[str, float]:
        """Get all available balances across all supported currencies"""
        try:
            all_balances = {}

            # Get comprehensive balance data
            balance_data = self.get_all_balances()

            for balance_key, balance_info in balance_data.items():
                currency = balance_info['coin']
                available = float(balance_info.get('available', 0))
                wallet_balance = float(balance_info.get('wallet_balance', 0))

                # Use the higher of available or wallet balance
                effective_balance = max(available, wallet_balance)

                if effective_balance > 0:
                    all_balances[currency] = effective_balance

            logger.info(f"[MULTI-CURRENCY] Found balances in {len(all_balances)} currencies: {list(all_balances.keys())}")

            # CRITICAL FIX: Remove balance caching - always use real-time data
            # No caching of balance data to ensure trading decisions use live API data

            return all_balances

        except Exception as e:
            logger.error(f"Error getting all available balances: {e}")
            return {}

    async def find_sufficient_balance_for_trade(self, symbol: str, side: str, required_amount: float,
                                              price: float) -> Dict:
        """Find sufficient balance across all available currencies for a trade"""
        try:
            normalized_symbol = self._normalize_symbol(symbol)
            base_currency, quote_currency = self.currency_manager.extract_base_quote_from_symbol(normalized_symbol)

            logger.info(f"[MULTI-CURRENCY] Finding balance for {side} {normalized_symbol}: "
                       f"Base={base_currency}, Quote={quote_currency}")

            # Get all available balances
            all_balances = await self.get_all_available_balances()

            if side.lower() == 'buy':
                # For buy orders, we need quote currency or equivalent
                primary_currency = quote_currency
                required_balance = required_amount  # For quote amount orders

                # Get alternative currencies that can be used
                alternative_currencies = self.currency_manager.get_alternative_quote_currencies(quote_currency)

            else:  # sell
                # For sell orders, we need base currency
                primary_currency = base_currency
                required_balance = required_amount
                alternative_currencies = [base_currency]  # Only base currency for sells

            # Add small buffer for fees
            required_with_fees = required_balance * 1.001

            # Check primary currency first
            if primary_currency in all_balances:
                available = all_balances[primary_currency]
                if available >= required_with_fees:
                    logger.info(f"[MULTI-CURRENCY] Sufficient {primary_currency} balance: "
                               f"{available:.6f} >= {required_with_fees:.6f}")
                    return {
                        'sufficient': True,
                        'currency': primary_currency,
                        'available_balance': available,
                        'required_balance': required_balance,
                        'required_with_fees': required_with_fees,
                        'is_primary': True,
                        'conversion_needed': False
                    }

            # INTELLIGENT CURRENCY ROUTING: Check alternative currencies for buy orders
            if side.lower() == 'buy':
                for alt_currency in alternative_currencies:
                    if alt_currency in all_balances and alt_currency != primary_currency:
                        available = all_balances[alt_currency]

                        # ENHANCED: Use intelligent currency routing for conversion calculation
                        conversion_info = self.currency_manager.calculate_conversion_rate(
                            alt_currency, primary_currency, available
                        )

                        effective_balance = conversion_info['converted_amount']
                        conversion_fees = conversion_info['fees']

                        if effective_balance >= required_with_fees:
                            logger.info(f"[INTELLIGENT-ROUTING] Sufficient {alt_currency} balance for conversion: "
                                       f"{available:.6f} -> {effective_balance:.6f} {primary_currency} "
                                       f"(fees: {conversion_fees:.6f}) >= {required_with_fees:.6f}")

                            # Get optimal trading route
                            trading_route = self.currency_manager.get_optimal_trading_route(
                                alt_currency, primary_currency, all_balances
                            )

                            return {
                                'sufficient': True,
                                'currency': alt_currency,
                                'available_balance': available,
                                'effective_balance': effective_balance,
                                'required_balance': required_balance,
                                'required_with_fees': required_with_fees,
                                'is_primary': False,
                                'conversion_needed': True,
                                'conversion_info': conversion_info,
                                'trading_route': trading_route,
                                'target_currency': primary_currency,
                                'routing_type': 'intelligent'
                            }

            # No sufficient balance found
            logger.warning(f"[MULTI-CURRENCY] Insufficient balance for {side} {normalized_symbol}")
            logger.info(f"[MULTI-CURRENCY] Available balances: {all_balances}")

            return {
                'sufficient': False,
                'error': f"Insufficient balance across all currencies for {side} {normalized_symbol}. "
                        f"Required: {required_with_fees:.6f} {primary_currency}, "
                        f"Available balances: {all_balances}",
                'required_balance': required_balance,
                'required_with_fees': required_with_fees,
                'available_balances': all_balances,
                'primary_currency': primary_currency
            }

        except Exception as e:
            logger.error(f"Error finding sufficient balance for trade: {e}")
            return {
                'sufficient': False,
                'error': f"Balance search error: {str(e)}",
                'required_balance': 0,
                'available_balances': {}
            }

    async def verify_sufficient_balance(self, symbol: str, side: str, quantity: float, price: Optional[float] = None) -> dict:
        """ENTERPRISE-GRADE: Multi-currency balance verification with intelligent routing"""
        try:
            normalized_symbol = self._normalize_symbol(symbol)

            # Get current price if not provided
            if price is None:
                price = float(self.get_price(normalized_symbol))
                if price <= 0:
                    return {
                        'sufficient': False,
                        'error': f"Could not get current price for {normalized_symbol}",
                        'required_balance': 0,
                        'available_balance': 0
                    }

            # Extract base and quote currencies
            base_currency, quote_currency = self.currency_manager.extract_base_quote_from_symbol(normalized_symbol)

            # CRITICAL FIX: Get REAL-TIME balance for the required currency
            if side.lower() == 'buy':
                # For buy orders, we need quote currency (USDT)
                required_currency = quote_currency
                required_amount = quantity  # Quote amount
            else:  # sell
                # For sell orders, we need base currency (BTC, ETH, etc.)
                required_currency = base_currency
                required_amount = quantity  # Base amount

            # Get real-time balance for the required currency
            try:
                actual_balance = float(await self.get_balance(required_currency))
                logger.info(f"🔍 [BALANCE-VERIFY] Real-time {required_currency} balance: {actual_balance:.8f}")
            except Exception as e:
                logger.error(f"❌ [BALANCE-VERIFY] Failed to get {required_currency} balance: {e}")
                return {
                    'sufficient': False,
                    'error': f"Could not get {required_currency} balance: {e}",
                    'required_balance': required_amount,
                    'available_balance': 0,
                    'currency': required_currency
                }

            # CRITICAL FIX: Use 80-90% of available balance as per user requirements
            # Never exceed actual available balance
            usable_balance = actual_balance * 0.85  # Use 85% for safety margin

            # Check if we have sufficient balance
            if usable_balance >= required_amount:
                logger.info(f"✅ [BALANCE-VERIFY] Sufficient {required_currency}: {usable_balance:.8f} >= {required_amount:.8f}")
                return {
                    'sufficient': True,
                    'currency': required_currency,
                    'required_balance': required_amount,
                    'available_balance': actual_balance,
                    'usable_balance': usable_balance,
                    'safety_margin': actual_balance - usable_balance
                }
            else:
                # CRITICAL FIX: If insufficient, try to use maximum available (80-90% of balance)
                if actual_balance > 0:
                    max_usable = actual_balance * 0.90  # Use up to 90% if needed
                    if max_usable >= required_amount * 0.5:  # At least 50% of requested amount
                        logger.info(f"✅ [BALANCE-OPTIMIZE] Optimizing from {required_amount:.8f} to {max_usable:.8f} {required_currency}")
                        return {
                            'sufficient': True,
                            'currency': required_currency,
                            'required_balance': max_usable,  # Adjusted amount
                            'available_balance': actual_balance,
                            'usable_balance': max_usable,
                            'adjusted': True,
                            'original_amount': required_amount
                        }

                logger.error(f"❌ [BALANCE-VERIFY] Insufficient {required_currency}: {actual_balance:.8f} < {required_amount:.8f}")
                return {
                    'sufficient': False,
                    'error': f"Insufficient {required_currency}: need {required_amount:.8f}, have {actual_balance:.8f}",
                    'currency': required_currency,
                    'required_balance': required_amount,
                    'available_balance': actual_balance
                }

        except Exception as e:
            logger.error(f"❌ [BALANCE-VERIFY] Error verifying sufficient balance for {symbol}: {e}")
            return {
                'sufficient': False,
                'error': f"Balance verification error: {str(e)}",
                'currency': 'unknown',
                'required_balance': quantity,
                'available_balance': 0
            }

    async def get_balance_snapshot(self) -> dict:
        """Get a comprehensive snapshot of all balances for verification"""
        try:
            snapshot = {
                'timestamp': time.time(),
                'balances': {},
                'total_usd_value': 0.0,
                'exchange': self.name
            }

            # Get all balances
            all_balances = self.get_all_balances()

            for balance_key, balance_data in all_balances.items():
                coin = balance_data['coin']
                wallet_balance = float(balance_data['wallet_balance'])

                if wallet_balance > 0:
                    snapshot['balances'][coin] = {
                        'balance': wallet_balance,
                        'account_type': balance_data['account_type'],
                        'available': float(balance_data['available']),
                        'used': float(balance_data['used'])
                    }

                    # Try to get USD value for major currencies
                    try:
                        if coin == 'USDT' or coin == 'USD':
                            usd_value = wallet_balance
                        elif coin == 'BTC':
                            btc_price = float(self.get_price('BTCUSDT'))
                            usd_value = wallet_balance * btc_price
                        elif coin == 'ETH':
                            eth_price = float(self.get_price('ETHUSDT'))
                            usd_value = wallet_balance * eth_price
                        else:
                            # Try to get price for other coins
                            try:
                                price = float(self.get_price(f"{coin}USDT"))
                                usd_value = wallet_balance * price
                            except:
                                usd_value = 0  # Can't determine USD value

                        snapshot['balances'][coin]['usd_value'] = usd_value
                        snapshot['total_usd_value'] += usd_value

                    except Exception as price_error:
                        logger.debug(f"Could not get USD value for {coin}: {price_error}")
                        snapshot['balances'][coin]['usd_value'] = 0

            logger.info(f"📊 [BALANCE-SNAPSHOT] Total USD value: ${snapshot['total_usd_value']:.2f}")
            return snapshot

        except Exception as e:
            logger.error(f"Error getting balance snapshot: {e}")
            return {
                'timestamp': time.time(),
                'balances': {},
                'total_usd_value': 0.0,
                'exchange': self.name,
                'error': str(e)
            }

    def test_connection(self):
        """Test the Bybit API connection."""
        if not self.session:
            logger.error("[ERROR] Bybit session not initialized.")
            return False
            
        try:
            logger.info("[AUTH] Testing Bybit API connection...")
            test_connection_response = self.session.get_account_info()
            if test_connection_response and test_connection_response.get("retCode") == 0:
                logger.info("[OK] Bybit API connection test successful.")
                self.is_authenticated = True
                return True
            else:
                error_msg = test_connection_response.get('retMsg', 'Unknown error') if test_connection_response else 'No response'
                logger.error(f"[ERROR] Bybit API connection test failed: {error_msg}")
                logger.error(f"Full response: {test_connection_response}")
                self.is_authenticated = False
                return False
        except Exception as e:
            logger.error(f"[ERROR] Bybit API connection test failed with exception: {e}")
            self.is_authenticated = False
            return False

    def _normalize_symbol(self, symbol: str) -> str:
        """Convert various symbol formats to Bybit's format - CRITICAL P4 FIX"""
        if not symbol:
            return "BTCUSDT"  # Default symbol

        # CRITICAL P4 FIX: Enhanced symbol normalization for Bybit-only mode
        symbol = symbol.strip().upper()

        # Handle Coinbase format symbols (BTC-USD) -> Bybit format (BTCUSDT)
        if "-" in symbol:
            base, quote = symbol.split("-", 1)  # Split only on first dash
            # CRITICAL: Convert USD to USDT for Bybit
            if quote == "USD":
                quote = "USDT"
            elif quote == "EUR":
                quote = "USDT"  # Convert EUR to USDT for Bybit
            normalized = f"{base}{quote}"
            logger.debug(f"🔄 [SYMBOL-CONVERT] {symbol} -> {normalized} (dash format)")
            return normalized

        elif "/" in symbol:
            base, quote = symbol.split("/", 1)  # Split only on first slash
            # CRITICAL: Convert USD to USDT for Bybit
            if quote == "USD":
                quote = "USDT"
            elif quote == "EUR":
                quote = "USDT"  # Convert EUR to USDT for Bybit
            normalized = f"{base}{quote}"
            logger.debug(f"🔄 [SYMBOL-CONVERT] {symbol} -> {normalized} (slash format)")
            return normalized

        else:
            # Already in Bybit format (BTCUSDT) or needs validation
            # CRITICAL P4 FIX: Validate against known Bybit symbols
            if self._is_valid_bybit_symbol(symbol):
                logger.debug(f"✅ [SYMBOL-VALID] {symbol} is valid Bybit format")
                return symbol
            else:
                # Try to convert common formats
                converted = self._convert_to_bybit_format(symbol)
                logger.debug(f"🔄 [SYMBOL-CONVERT] {symbol} -> {converted} (format conversion)")
                return converted

    def _is_valid_bybit_symbol(self, symbol: str) -> bool:
        """REAL EXCHANGE DATA VALIDATION - Uses actual Bybit API to validate symbols"""
        try:
            # Initialize caches if not exists
            if not hasattr(self, '_symbol_validation_cache'):
                self._symbol_validation_cache = {}
                self._symbol_cache_timestamp = {}
                self._exchange_symbols_cache = None
                self._exchange_symbols_cache_time = 0

            # Check cache first (1 hour for valid symbols, 5 minutes for invalid)
            current_time = time.time()
            if symbol in self._symbol_validation_cache:
                cache_time = self._symbol_cache_timestamp.get(symbol, 0)
                is_valid = self._symbol_validation_cache[symbol]

                # Use different cache durations: 1 hour for valid, 5 minutes for invalid
                cache_duration = 3600 if is_valid else 300  # 1 hour vs 5 minutes

                if current_time - cache_time < cache_duration:
                    logger.debug(f"[CACHE-HIT] Symbol {symbol}: {'VALID' if is_valid else 'INVALID'}")
                    return is_valid

            # Get real exchange data if cache is stale or missing
            is_valid = self._validate_symbol_with_exchange_api(symbol)

            # Cache the result with timestamp
            self._symbol_validation_cache[symbol] = is_valid
            self._symbol_cache_timestamp[symbol] = current_time

            logger.debug(f"[API-VALIDATED] Symbol {symbol}: {'VALID' if is_valid else 'INVALID'}")
            return is_valid

        except Exception as e:
            logger.debug(f"Error validating symbol {symbol} with exchange API: {e}")
            # On error, assume invalid to be safe
            return False

    def _validate_symbol_with_exchange_api(self, symbol: str) -> bool:
        """Validate symbol using actual Bybit API data"""
        try:
            # Get all available trading pairs from exchange
            exchange_symbols = self._get_all_exchange_symbols()

            if not exchange_symbols:
                logger.warning(f"[API-ERROR] Could not fetch exchange symbols for validation")
                return False

            # Check if symbol exists in exchange data
            symbol_upper = symbol.upper()

            # Direct match
            if symbol_upper in exchange_symbols:
                return True

            # Try common format variations
            variations = [
                symbol_upper,
                symbol_upper.replace('-', ''),
                symbol_upper.replace('/', ''),
                symbol_upper.replace('_', ''),
            ]

            for variation in variations:
                if variation in exchange_symbols:
                    logger.debug(f"[FORMAT-MATCH] {symbol} matched as {variation}")
                    return True

            return False

        except Exception as e:
            logger.debug(f"Error validating {symbol} with exchange API: {e}")
            return False

    def _get_all_exchange_symbols(self) -> set:
        """Get all available symbols from Bybit API with caching"""
        try:
            current_time = time.time()

            # Check if we have fresh exchange data (refresh every 10 minutes)
            if (self._exchange_symbols_cache is not None and
                current_time - self._exchange_symbols_cache_time < 600):
                return self._exchange_symbols_cache

            logger.info("[EXCHANGE-API] Fetching all available trading pairs from Bybit...")

            # Use the market discovery system to get all instruments
            if not self.currency_manager or not self.currency_manager.market_discovery:
                logger.error("[EXCHANGE-API] Market discovery not available")
                return set()

            instruments = self.currency_manager.market_discovery.get_all_instruments("spot")

            if not instruments:
                logger.error("[EXCHANGE-API] No instruments returned from Bybit API")
                return set()

            # Extract all active trading symbols
            exchange_symbols = set()
            for instrument in instruments:
                symbol = instrument.get("symbol", "")
                status = instrument.get("status", "")

                # Only include active/trading symbols
                if status.lower() in ["trading", "active"] and symbol:
                    exchange_symbols.add(symbol.upper())

            # Cache the results
            self._exchange_symbols_cache = exchange_symbols
            self._exchange_symbols_cache_time = current_time

            logger.info(f"[EXCHANGE-API] Cached {len(exchange_symbols)} active trading pairs from Bybit")
            return exchange_symbols

        except Exception as e:
            logger.error(f"[EXCHANGE-API] Error fetching symbols from Bybit: {e}")
            return set()

    def _convert_to_bybit_format(self, symbol: str) -> str:
        """Convert various symbol formats to Bybit format"""
        # Handle common conversions
        conversions = {
            'BTCUSD': 'BTCUSDT',
            'ETHUSD': 'ETHUSDT',
            'SOLUSD': 'SOLUSDT',
            'ADAUSD': 'ADAUSDT',
            'DOTUSD': 'DOTUSDT',
            'LINKUSD': 'LINKUSDT',
            'UNIUSD': 'UNIUSDT',
            'AVAXUSD': 'AVAXUSDT',
            # CRITICAL FIX: Remove MATICUSDT as it's not supported on Bybit
            # Handle EUR conversions
            'BTCEUR': 'BTCUSDT',
            'ETHEUR': 'ETHUSDT',
            'SOLEUR': 'SOLUSDT',
            'ADAEUR': 'ADAUSDT',
            'DOTEUR': 'DOTUSDT'
        }

        if symbol in conversions:
            return conversions[symbol]

        # If symbol ends with USD, convert to USDT
        if symbol.endswith('USD') and len(symbol) > 3:
            base = symbol[:-3]
            return f"{base}USDT"

        # If symbol ends with EUR, convert to USDT
        if symbol.endswith('EUR') and len(symbol) > 3:
            base = symbol[:-3]
            return f"{base}USDT"

        # Default: assume it's already correct or return as-is
        return symbol

    async def auto_fetch_all_precisions(self):
        """AUTOMATED: Fetch precision for all major trading pairs at startup (FAST VERSION)"""
        # Reduced list for faster startup
        major_symbols = ['BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'ADAUSDT', 'DOTUSDT']

        logger.info("🔄 [AUTO-PRECISION] Fast-fetching precision for core symbols...")
        start_time = time.time()

        # PERFORMANCE OPTIMIZATION: Initialize cache first
        if not hasattr(self, '_dynamic_precision_cache'):
            self._dynamic_precision_cache = {}

        # Use concurrent fetching for speed
        tasks = []
        for symbol in major_symbols:
            task = asyncio.create_task(self._fetch_single_precision(symbol))
            tasks.append(task)

        # Wait for all with aggressive timeout for speed
        try:
            await asyncio.wait_for(asyncio.gather(*tasks, return_exceptions=True), timeout=5.0)  # Reduced from 30s to 5s
            elapsed_time = (time.time() - start_time) * 1000
            logger.info(f"✅ [AUTO-PRECISION] Fast precision fetch completed in {elapsed_time:.1f}ms")

            # PERFORMANCE VALIDATION: Check if we met our speed targets
            if elapsed_time > 5000:  # 5 seconds
                logger.warning(f"⚠️ [PERFORMANCE] Precision fetching took {elapsed_time:.1f}ms (target: <5000ms)")

        except asyncio.TimeoutError:
            logger.warning("⚠️ [AUTO-PRECISION] Precision fetch timed out, using defaults")

    async def _fetch_single_precision(self, symbol: str):
        """Fetch precision for a single symbol with error handling"""
        try:
            await self.update_precision_from_api(symbol)
        except Exception as e:
            logger.debug(f"⚠️ [AUTO-PRECISION] Failed to fetch precision for {symbol}: {e}")

    def _get_bybit_minimum_requirements(self, symbol: str) -> dict:
        """Get Bybit's current minimum order requirements for a symbol with dynamic precision loading"""
        # PERFORMANCE OPTIMIZATION: Check cache first for speed (<100ms target)
        start_time = time.time()

        # Check for runtime precision updates first
        if hasattr(self, '_dynamic_precision_cache') and symbol in self._dynamic_precision_cache:
            cached_requirements = self._dynamic_precision_cache[symbol]
            # CRITICAL FIX: Extend cache validity to 30 minutes for better performance
            cache_age_seconds = (datetime.now() - cached_requirements.get('last_updated', datetime.min)).total_seconds()
            if cache_age_seconds < 1800:  # 30 minutes cache
                cache_time = (time.time() - start_time) * 1000
                logger.debug(f"[CACHE-HIT] {symbol} precision retrieved in {cache_time:.1f}ms (cached {cache_age_seconds:.0f}s ago)")
                return cached_requirements

        # CRITICAL FIX: Updated Bybit minimum requirements based on ErrCode 170140 research
        # Official Bybit documentation confirms minimum order values to prevent 170140 errors
        symbol_requirements = {
            'BTCUSDT': {
                'min_order_value_usdt': 5.0,        # CRITICAL FIX: Reduced to $5 for micro-trading
                'min_qty': 0.000011,               # Real minimum BTC quantity from API
                'qty_precision': 4,                # FIXED: Reduced precision to prevent ErrCode 170137
                'price_precision': 2,              # Decimal places for price
                'tick_size': 0.1,                  # Real tick size from API
                'last_updated': datetime.now()     # ENTERPRISE: Track update time
            },
            'USDCUSDT': {
                'min_order_value_usdt': 5.0,        # CRITICAL FIX: Reduced to $5 for micro-trading
                'min_qty': 0.01,                   # Minimum USDC quantity
                'qty_precision': 2,                # FIXED: USDC only needs 2 decimal places
                'price_precision': 4,              # Price precision for USDC
                'tick_size': 0.0001,               # Tick size
                'last_updated': datetime.now()
            },
            'ETHUSDT': {
                'min_order_value_usdt': 5.0,        # CRITICAL FIX: Reduced to $5 for micro-trading
                'min_qty': 0.00029,                # Real minimum ETH quantity from API
                'qty_precision': 4,                # CRITICAL FIX: Updated to 4 decimals (was 6)
                'price_precision': 2,
                'tick_size': 0.01,
                'last_updated': datetime.now()     # ENTERPRISE: Track update time
            },
            'SOLUSDT': {
                'min_order_value_usdt': 5.0,        # CRITICAL FIX: Reduced to $5 for micro-trading
                'min_qty': 0.006,                  # Real minimum SOL quantity from API
                'qty_precision': 3,                # CRITICAL FIX: Reduced from 4 to 3 decimals to prevent "too many decimals" error
                'price_precision': 3,
                'tick_size': 0.01,
                'last_updated': datetime.now()     # ENTERPRISE: Track update time
            },
            'ADAUSDT': {
                'min_order_value_usdt': 5.0,        # CRITICAL FIX: Reduced to $5 for micro-trading
                'min_qty': 1.07,                   # Real minimum ADA quantity from API
                'qty_precision': 1,                # ADA requires only 1 decimal place
                'price_precision': 4,
                'tick_size': 0.0001
            },
            'DOTUSDT': {
                'min_order_value_usdt': 5.0,        # CRITICAL FIX: Reduced to $5 for micro-trading
                'min_qty': 0.133,                  # Real minimum DOT quantity from API
                'qty_precision': 3,                # CRITICAL FIX: Reduced from 6 to 3 decimals to prevent "too many decimals" error
                'price_precision': 3,
                'tick_size': 0.001
            },
            'BNBUSDT': {
                'min_order_value_usdt': 5.0,        # CRITICAL FIX: Reduced to $5 for micro-trading
                'min_qty': 0.001,                  # Minimum BNB quantity
                'qty_precision': 3,                # CRITICAL FIX: Reduced from 6 to 3 decimals
                'price_precision': 2,
                'tick_size': 0.01
            },
            'XRPUSDT': {
                'min_order_value_usdt': 5.0,        # CRITICAL FIX: Reduced to $5 for micro-trading
                'min_qty': 0.44,                   # Minimum XRP quantity (from API)
                'qty_precision': 2,                # CRITICAL: XRP requires exactly 2 decimals
                'price_precision': 4,
                'tick_size': 0.0001
            }
        }

        # CRITICAL FIX: Default requirements based on actual Bybit API behavior
        default_requirements = {
            'min_order_value_usdt': 5.0,    # CRITICAL FIX: Reduced to $5 for micro-trading (was $10)
            'min_qty': 0.0001,             # Minimum quantity
            'qty_precision': 2,            # CRITICAL FIX: Reduced to 2 decimals to prevent "too many decimals" error
            'price_precision': 4,
            'tick_size': 0.0001,
            'last_updated': datetime.now()  # ENTERPRISE: Track update time
        }

        # Cache the requirements for dynamic updates
        if not hasattr(self, '_dynamic_precision_cache'):
            self._dynamic_precision_cache = {}

        requirements = symbol_requirements.get(symbol, default_requirements)

        # Cache for dynamic updates
        self._dynamic_precision_cache[symbol] = requirements

        return requirements

    def reload_precision_configuration(self, symbol: str = None):
        """ENTERPRISE-GRADE: Reload precision configuration without system restart"""
        try:
            if symbol:
                # Reload specific symbol
                if hasattr(self, '_dynamic_precision_cache') and symbol in self._dynamic_precision_cache:
                    del self._dynamic_precision_cache[symbol]
                    logger.info(f"🔄 [PRECISION-RELOAD] Reloaded precision config for {symbol}")

                    # Get fresh requirements
                    fresh_requirements = self._get_bybit_minimum_requirements(symbol)
                    logger.info(f"📊 [PRECISION-UPDATE] {symbol}: precision={fresh_requirements['qty_precision']}, min_qty={fresh_requirements['min_qty']}")
            else:
                # Reload all cached symbols
                if hasattr(self, '_dynamic_precision_cache'):
                    symbols_reloaded = list(self._dynamic_precision_cache.keys())
                    self._dynamic_precision_cache.clear()
                    logger.info(f"🔄 [PRECISION-RELOAD] Cleared all precision cache for symbols: {symbols_reloaded}")

                    # Reload each symbol
                    for sym in symbols_reloaded:
                        fresh_requirements = self._get_bybit_minimum_requirements(sym)
                        logger.info(f"📊 [PRECISION-UPDATE] {sym}: precision={fresh_requirements['qty_precision']}")

        except Exception as e:
            logger.error(f"❌ [PRECISION-RELOAD] Error reloading precision config: {e}")

    async def update_precision_from_api(self, symbol: str):
        """OPTIMIZED: Fast precision update with caching and timeout protection"""
        try:
            # PERFORMANCE FIX: Check cache first to avoid API calls
            if hasattr(self, '_dynamic_precision_cache') and symbol in self._dynamic_precision_cache:
                cache_entry = self._dynamic_precision_cache[symbol]
                cache_age = (datetime.now() - cache_entry.get('last_updated', datetime.min)).total_seconds()

                # Use cache if less than 1 hour old
                if cache_age < 3600:
                    logger.debug(f"🚀 [PRECISION-CACHE] Using cached precision for {symbol} (age: {cache_age:.0f}s)")
                    return True

            start_time = time.time()

            # PERFORMANCE FIX: Use async timeout to prevent hanging
            try:
                # Get instrument info from Bybit API with timeout
                response = await asyncio.wait_for(
                    asyncio.get_event_loop().run_in_executor(
                        None,
                        lambda: self.session.get_instruments_info(category="spot", symbol=symbol)
                    ),
                    timeout=2.0  # 2 second timeout
                )

                if response.get('retCode') == 0 and response.get('result', {}).get('list'):
                    instrument = response['result']['list'][0]

                    # Extract precision from API response
                    lot_size_filter = instrument.get('lotSizeFilter', {})
                    price_filter = instrument.get('priceFilter', {})

                    # Calculate precision from step sizes
                    qty_step = float(lot_size_filter.get('qtyStep', '0.0001'))
                    tick_size = float(price_filter.get('tickSize', '0.01'))

                    # CRITICAL FIX: Get minimum order amount from API
                    min_order_amt = float(lot_size_filter.get('minOrderAmt', '5.0'))
                    min_order_qty = float(lot_size_filter.get('minOrderQty', '0.000001'))

                    # CRITICAL FIX: Calculate decimal precision more accurately
                    def get_decimal_places(value_str):
                        """Get number of decimal places, handling scientific notation"""
                        if 'e' in str(value_str).lower():
                            # Handle scientific notation like 1e-6
                            return abs(int(str(value_str).lower().split('e')[1]))
                        elif '.' in str(value_str):
                            return len(str(value_str).split('.')[1])
                        else:
                            return 0

                    qty_precision = get_decimal_places(qty_step)
                    price_precision = get_decimal_places(tick_size)

                    # CRITICAL FIX: Apply symbol-specific precision rules based on real API data
                    # These are the ACTUAL precision requirements from Bybit API testing
                    symbol_specific_precision = {
                        'ADAUSDT': 1,    # ADA requires exactly 1 decimal place
                        'SOLUSDT': 3,    # SOL requires exactly 3 decimal places
                        'BTCUSDT': 5,    # BTC can handle 5 decimal places
                        'ETHUSDT': 4,    # ETH requires 4 decimal places
                        'DOTUSDT': 3,    # DOT requires 3 decimal places
                        'XRPUSDT': 2,    # XRP requires exactly 2 decimal places
                    }

                    # Use symbol-specific precision if available, otherwise cap at 3 for safety
                    if symbol in symbol_specific_precision:
                        qty_precision = symbol_specific_precision[symbol]
                        logger.info(f"🎯 [PRECISION-API] Using symbol-specific precision for {symbol}: {qty_precision} decimals")
                    else:
                        qty_precision = min(qty_precision, 3)  # Cap at 3 decimals for unknown symbols
                        logger.warning(f"⚠️ [PRECISION-API] Using capped precision for {symbol}: {qty_precision} decimals")

                    price_precision = min(price_precision, 6)  # Price can be more precise

                    # Update cache with API data
                    if not hasattr(self, '_dynamic_precision_cache'):
                        self._dynamic_precision_cache = {}

                    self._dynamic_precision_cache[symbol] = {
                        'min_order_value_usdt': float(lot_size_filter.get('minOrderAmt', '5.0')),
                        'min_qty': float(lot_size_filter.get('minOrderQty', '0.001')),
                        'qty_precision': qty_precision,
                        'price_precision': price_precision,
                        'tick_size': tick_size,
                        'qty_step': qty_step,
                        'last_updated': datetime.now(),
                        'source': 'api'  # Mark as API-sourced
                    }

                    # PERFORMANCE OPTIMIZATION: Log timing for speed monitoring
                    elapsed_time = (time.time() - start_time) * 1000  # Convert to milliseconds
                    logger.info(f"✅ [API-PRECISION] Updated {symbol} in {elapsed_time:.1f}ms: qty_precision={qty_precision}, min_amt=${min_order_amt:.2f}")

                    # CRITICAL FIX: Validate precision meets performance targets (<100ms)
                    if elapsed_time > 100:
                        logger.warning(f"⚠️ [PERFORMANCE] Precision update for {symbol} took {elapsed_time:.1f}ms (target: <100ms)")

                    return True
                else:
                    logger.warning(f"⚠️ [PRECISION-API] Failed to get instrument info for {symbol}: {response}")
                    # Fallback to hardcoded precision
                    return self._use_fallback_precision(symbol)

            except asyncio.TimeoutError:
                logger.warning(f"⚠️ [AUTO-PRECISION] Precision fetch timed out, using defaults")
                return self._use_fallback_precision(symbol)

        except Exception as e:
            logger.error(f"❌ [API-PRECISION] Failed to update precision from API for {symbol}: {e}")
            return self._use_fallback_precision(symbol)

    def _use_fallback_precision(self, symbol: str) -> bool:
        """Use hardcoded fallback precision for performance"""
        try:
            # Hardcoded precision for common symbols (for speed)
            fallback_precision = {
                'BTCUSDT': {'qty_precision': 5, 'price_precision': 2, 'min_order_value_usdt': 5.0},
                'ETHUSDT': {'qty_precision': 4, 'price_precision': 2, 'min_order_value_usdt': 5.0},
                'ADAUSDT': {'qty_precision': 1, 'price_precision': 4, 'min_order_value_usdt': 5.0},
                'SOLUSDT': {'qty_precision': 3, 'price_precision': 3, 'min_order_value_usdt': 5.0},
                'DOTUSDT': {'qty_precision': 3, 'price_precision': 3, 'min_order_value_usdt': 5.0},
                'AVAXUSDT': {'qty_precision': 3, 'price_precision': 3, 'min_order_value_usdt': 5.0},
                'LINKUSDT': {'qty_precision': 3, 'price_precision': 3, 'min_order_value_usdt': 5.0},
                'UNIUSDT': {'qty_precision': 3, 'price_precision': 3, 'min_order_value_usdt': 5.0},
                'ATOMUSDT': {'qty_precision': 3, 'price_precision': 3, 'min_order_value_usdt': 5.0},
                'LTCUSDT': {'qty_precision': 4, 'price_precision': 2, 'min_order_value_usdt': 5.0}
            }

            if symbol in fallback_precision:
                if not hasattr(self, '_dynamic_precision_cache'):
                    self._dynamic_precision_cache = {}

                self._dynamic_precision_cache[symbol] = {
                    **fallback_precision[symbol],
                    'min_qty': 0.001,
                    'tick_size': 0.01,
                    'qty_step': 0.001,
                    'last_updated': datetime.now(),
                    'source': 'fallback'
                }

                logger.info(f"🚀 [PRECISION-FALLBACK] Using hardcoded precision for {symbol}")
                return True

            return False

        except Exception as e:
            logger.error(f"❌ [PRECISION-FALLBACK] Error setting fallback precision: {e}")
            return False

    def preload_precision_cache(self):
        """PERFORMANCE OPTIMIZATION: Preload precision cache with hardcoded values for speed"""
        try:
            # Initialize cache if not exists
            if not hasattr(self, '_dynamic_precision_cache'):
                self._dynamic_precision_cache = {}

            # CRITICAL FIX: Preload with known working precision values to avoid API calls
            precision_preload = {
                'ADAUSDT': {
                    'min_order_value_usdt': 5.0,
                    'min_qty': 1.07,
                    'qty_precision': 1,  # CRITICAL: ADA requires exactly 1 decimal
                    'price_precision': 4,
                    'tick_size': 0.0001,
                    'last_updated': datetime.now(),
                    'source': 'preload'
                },
                'SOLUSDT': {
                    'min_order_value_usdt': 5.0,
                    'min_qty': 0.006,
                    'qty_precision': 3,  # CRITICAL: SOL requires exactly 3 decimals
                    'price_precision': 3,
                    'tick_size': 0.01,
                    'last_updated': datetime.now(),
                    'source': 'preload'
                },
                'BTCUSDT': {
                    'min_order_value_usdt': 5.0,
                    'min_qty': 0.000011,
                    'qty_precision': 5,  # BTC can handle 5 decimals
                    'price_precision': 2,
                    'tick_size': 0.1,
                    'last_updated': datetime.now(),
                    'source': 'preload'
                },
                'ETHUSDT': {
                    'min_order_value_usdt': 5.0,
                    'min_qty': 0.00007,
                    'qty_precision': 4,  # ETH requires 4 decimals
                    'price_precision': 2,
                    'tick_size': 0.01,
                    'last_updated': datetime.now(),
                    'source': 'preload'
                },
                'DOTUSDT': {
                    'min_order_value_usdt': 5.0,
                    'min_qty': 0.133,
                    'qty_precision': 3,  # DOT requires 3 decimals
                    'price_precision': 3,
                    'tick_size': 0.001,
                    'last_updated': datetime.now(),
                    'source': 'preload'
                },
                'XRPUSDT': {
                    'min_order_value_usdt': 5.0,
                    'min_qty': 0.44,
                    'qty_precision': 2,  # CRITICAL: XRP requires exactly 2 decimals
                    'price_precision': 4,
                    'tick_size': 0.0001,
                    'last_updated': datetime.now(),
                    'source': 'preload'
                }
            }

            # Load into cache
            for symbol, requirements in precision_preload.items():
                self._dynamic_precision_cache[symbol] = requirements

            logger.info(f"✅ [PRECISION-PRELOAD] Loaded {len(precision_preload)} symbol precisions for instant access")

        except Exception as e:
            logger.error(f"❌ [PRECISION-PRELOAD] Error preloading precision cache: {e}")

    async def get_crypto_holdings_for_trading(self) -> dict:
        """Get available crypto holdings that can be used for SELL orders"""
        try:
            holdings = {}
            all_balances = self.get_all_balances()

            # Extract crypto holdings with sufficient balance for trading
            for balance_key, balance_data in all_balances.items():
                coin = balance_data["coin"]
                available = float(balance_data["available"])

                # Skip USDT and focus on crypto holdings
                if coin != "USDT" and available > 0:
                    # Check if we have enough for minimum order value
                    symbol = f"{coin}USDT"
                    try:
                        current_price = float(self.get_price(symbol))
                        if current_price > 0:
                            holding_value_usd = available * current_price
                            if holding_value_usd >= 10.0:  # Minimum $10 order value (actual Bybit API behavior)
                                holdings[coin] = {
                                    'available': available,
                                    'price': current_price,
                                    'value_usd': holding_value_usd,
                                    'symbol': symbol
                                }
                                logger.info(f"💰 [CRYPTO-HOLDINGS] {coin}: {available:.6f} (${holding_value_usd:.2f})")
                    except Exception as e:
                        logger.debug(f"Could not get price for {symbol}: {e}")

            return holdings
        except Exception as e:
            logger.error(f"Error getting crypto holdings: {e}")
            return {}

    async def determine_optimal_order_strategy(self, symbol: str, intended_side: str, amount: float) -> dict:
        """Determine optimal order strategy based on available balances and minimum requirements"""
        try:
            # CRITICAL FIX: Get REAL-TIME USDT balance with timestamp logging
            balance_fetch_time = time.time()
            try:
                usdt_balance = float(await self.get_balance('USDT'))
                logger.info(f"✅ [REAL-TIME-BALANCE] USDT Balance: ${usdt_balance:.2f} (LIVE from Bybit API at {balance_fetch_time})")
            except Exception as balance_error:
                logger.error(f"❌ [BALANCE-CRITICAL] Failed to get real-time USDT balance: {balance_error}")
                return {
                    'strategy': 'error',
                    'error': f"Real-time USDT balance unavailable: {balance_error}"
                }

            # Get current price for the symbol
            current_price = float(self.get_price(symbol))
            if current_price <= 0:
                return {
                    'strategy': 'error',
                    'error': f"Could not get current price for {symbol}"
                }

            # CRITICAL P1 FIX: Implement automatic SELL order switching for $5 minimum requirement
            min_order_value_usd = 5.0  # Bybit's $5 minimum order requirement (REAL API data)

            if intended_side.lower() == 'buy':
                # Check if BUY order meets minimum requirement
                order_value = amount * current_price if amount > 0 else amount  # amount might be in USD already

                if usdt_balance < min_order_value_usd:
                    logger.warning(f"🔄 [ORDER-SWITCH] USDT balance ${usdt_balance:.2f} < $10 minimum - switching to SELL order strategy")

                    # Get available crypto holdings for SELL orders
                    base_currency = symbol.replace('USDT', '').replace('USD', '')

                    try:
                        crypto_balance = float(await self.get_balance(base_currency))
                        logger.info(f"📊 [CRYPTO-BALANCE] {base_currency} balance: {crypto_balance:.6f}")

                        if crypto_balance > 0:
                            # Use 80-90% of crypto holdings for SELL order (as per user requirements)
                            sell_percentage = 0.85  # 85% of holdings
                            sell_amount = crypto_balance * sell_percentage
                            sell_value = sell_amount * current_price

                            if sell_value >= min_order_value_usd:
                                logger.info(f"✅ [ORDER-SWITCH] Switching to SELL {sell_amount:.6f} {base_currency} (${sell_value:.2f} value)")
                                return {
                                    'strategy': 'sell_crypto',
                                    'side': 'sell',
                                    'amount': sell_amount,
                                    'symbol': symbol,
                                    'reason': f"USDT balance ${usdt_balance:.2f} < $10 minimum, using {sell_percentage*100}% of {base_currency} holdings (${sell_value:.2f})"
                                }
                            else:
                                logger.warning(f"⚠️ [ORDER-SWITCH] {base_currency} holdings too small: ${sell_value:.2f} < $10 minimum")
                        else:
                            logger.warning(f"⚠️ [ORDER-SWITCH] No {base_currency} holdings available for SELL order")

                    except Exception as crypto_error:
                        logger.error(f"❌ [CRYPTO-BALANCE] Failed to get {base_currency} balance: {crypto_error}")

                    # Try other major crypto holdings
                    major_cryptos = ['BTC', 'ETH', 'SOL', 'ADA', 'DOT', 'LINK', 'UNI', 'AVAX', 'MATIC']
                    for crypto in major_cryptos:
                        if crypto == base_currency:
                            continue  # Already checked

                        try:
                            crypto_balance = float(await self.get_balance(crypto))
                            if crypto_balance > 0:
                                # Get price for this crypto
                                crypto_symbol = f"{crypto}USDT"
                                crypto_price = float(self.get_price(crypto_symbol))

                                if crypto_price > 0:
                                    sell_amount = crypto_balance * 0.85  # 85% of holdings
                                    sell_value = sell_amount * crypto_price

                                    if sell_value >= min_order_value_usd:
                                        logger.info(f"✅ [ORDER-SWITCH] Found alternative: SELL {sell_amount:.6f} {crypto} (${sell_value:.2f} value)")
                                        return {
                                            'strategy': 'sell_crypto',
                                            'side': 'sell',
                                            'amount': sell_amount,
                                            'symbol': crypto_symbol,
                                            'reason': f"USDT balance ${usdt_balance:.2f} < $10 minimum, using 85% of {crypto} holdings (${sell_value:.2f})"
                                        }
                        except Exception as e:
                            logger.debug(f"🔍 [CRYPTO-CHECK] Error checking {crypto}: {e}")
                            continue

                    # If no crypto holdings are sufficient
                    return {
                        'strategy': 'insufficient_funds',
                        'error': f"USDT balance ${usdt_balance:.2f} < $10 minimum and no sufficient crypto holdings for SELL orders"
                    }

                elif order_value < min_order_value_usd:
                    # Adjust BUY order amount to meet minimum
                    adjusted_amount = min_order_value_usd / current_price
                    adjusted_value = adjusted_amount * current_price

                    if usdt_balance >= adjusted_value:
                        logger.info(f"📊 [ORDER-ADJUST] Adjusting BUY order to meet $10 minimum: {adjusted_amount:.6f} (${adjusted_value:.2f})")
                        return {
                            'strategy': 'buy_adjusted',
                            'side': 'buy',
                            'amount': adjusted_amount,
                            'symbol': symbol,
                            'reason': f"Adjusted BUY amount to meet $10 minimum requirement"
                        }
                    else:
                        return {
                            'strategy': 'insufficient_funds',
                            'error': f"USDT balance ${usdt_balance:.2f} insufficient for minimum $10 order"
                        }
                else:
                    # Original BUY order is fine
                    return {
                        'strategy': 'buy',
                        'side': 'buy',
                        'amount': amount,
                        'symbol': symbol,
                        'reason': f"Sufficient USDT balance (${usdt_balance:.2f}) for BUY order"
                    }

            # CRITICAL FIX: Implement proper position sizing logic (20-25% of available balance)
            if intended_side.lower() == 'buy':
                # Calculate position size as percentage of available balance (NOT full balance)
                position_percentage = 0.225  # 22.5% default position sizing
                calculated_trade_amount = usdt_balance * position_percentage

                # Use the smaller of: calculated position size OR requested amount
                actual_trade_amount = min(amount, calculated_trade_amount) if amount else calculated_trade_amount

                # Calculate required USDT for this trade
                required_usdt = actual_trade_amount * current_price

                logger.info(f"📊 [POSITION-SIZING] Available USDT: ${usdt_balance:.2f} (LIVE from API)")
                logger.info(f"📊 [POSITION-SIZING] Position percentage: {position_percentage*100:.1f}%")
                logger.info(f"📊 [POSITION-SIZING] Calculated trade amount: ${calculated_trade_amount:.2f}")
                logger.info(f"📊 [POSITION-SIZING] Actual trade amount: ${actual_trade_amount:.2f}")
                logger.info(f"📊 [POSITION-SIZING] Required USDT: ${required_usdt:.2f}")
                logger.info(f"📊 [POSITION-SIZING] Minimum required: $10.00")

                # CRITICAL FIX: Only report insufficient funds when calculated trade amount < $10 minimum
                if required_usdt >= 10.0 and usdt_balance >= required_usdt:
                    logger.info(f"✅ [STRATEGY-DECISION] BUY strategy approved - sufficient funds")
                    return {
                        'strategy': 'buy',
                        'side': 'buy',
                        'amount': actual_trade_amount,
                        'symbol': symbol,
                        'reason': f"BUY order approved: ${required_usdt:.2f} required, ${usdt_balance:.2f} available ({position_percentage*100:.1f}% position sizing)"
                    }
                elif calculated_trade_amount < 10.0:
                    logger.warning(f"⚠️ [STRATEGY-DECISION] Calculated trade amount ${calculated_trade_amount:.2f} below $10 minimum")
                    # Try to find crypto to sell instead
                    crypto_holdings = await self.get_crypto_holdings_for_trading()

                    if crypto_holdings:
                        # Find the best crypto to sell
                        best_crypto = max(crypto_holdings.items(), key=lambda x: x[1]['value_usd'])
                        crypto_symbol = best_crypto[0]
                        holding_data = best_crypto[1]

                        # Calculate sell amount (use 80-90% of available balance)
                        sell_amount = holding_data['available'] * 0.85  # 85% of available
                        sell_value = sell_amount * holding_data['price']

                        if sell_value >= 10.0:
                            logger.info(f"🔄 [STRATEGY-DECISION] Switching to SELL {crypto_symbol} strategy")
                            return {
                                'strategy': 'sell_crypto',
                                'side': 'sell',
                                'amount': sell_amount,
                                'symbol': holding_data['symbol'],
                                'reason': f"Switched to SELL {crypto_symbol} (${sell_value:.2f}) - USDT balance too low for minimum trade"
                            }

                    logger.error(f"❌ [STRATEGY-DECISION] Insufficient funds: calculated trade ${calculated_trade_amount:.2f} < $10 minimum, no suitable crypto holdings")
                    return {
                        'strategy': 'api_error',
                        'error': f"Real-time balance data shows insufficient funds: calculated trade amount ${calculated_trade_amount:.2f} below $10 minimum, no suitable crypto holdings available"
                    }
                else:
                    logger.warning(f"⚠️ [STRATEGY-DECISION] Insufficient USDT: need ${required_usdt:.2f}, have ${usdt_balance:.2f}")
                    # Try to find crypto to sell instead
                    crypto_holdings = await self.get_crypto_holdings_for_trading()

                    if crypto_holdings:
                        # Find the best crypto to sell
                        best_crypto = max(crypto_holdings.items(), key=lambda x: x[1]['value_usd'])
                        crypto_symbol = best_crypto[0]
                        holding_data = best_crypto[1]

                        # Calculate sell amount (use 80-90% of available balance)
                        sell_amount = holding_data['available'] * 0.85  # 85% of available
                        sell_value = sell_amount * holding_data['price']

                        if sell_value >= 10.0:
                            logger.info(f"🔄 [STRATEGY-DECISION] Switching to SELL {crypto_symbol} strategy")
                            return {
                                'strategy': 'sell_crypto',
                                'side': 'sell',
                                'amount': sell_amount,
                                'symbol': holding_data['symbol'],
                                'reason': f"Switched to SELL {crypto_symbol} (${sell_value:.2f}) due to insufficient USDT for requested trade size"
                            }

                    logger.error(f"❌ [STRATEGY-DECISION] Insufficient USDT and no suitable crypto holdings")
                    return {
                        'strategy': 'api_error',
                        'error': f"Real-time balance data unavailable from Bybit at {balance_fetch_time}: insufficient USDT (${usdt_balance:.2f}) for trade (${required_usdt:.2f}) and no suitable crypto holdings"
                    }

            else:  # SELL order
                base_currency = symbol.replace('USDT', '').replace('USD', '')
                try:
                    base_balance = float(await self.get_balance(base_currency))
                    logger.info(f"📊 [SELL-BALANCE] {base_currency} balance: {base_balance:.6f}")
                except Exception as balance_error:
                    logger.error(f"❌ [SELL-BALANCE] Failed to get {base_currency} balance: {balance_error}")
                    return {
                        'strategy': 'error',
                        'error': f"Could not get {base_currency} balance: {balance_error}"
                    }

                if base_balance >= amount:
                    order_value = amount * current_price
                    if order_value >= min_order_value_usd:
                        return {
                            'strategy': 'sell',
                            'side': 'sell',
                            'amount': amount,
                            'symbol': symbol,
                            'reason': f"Sufficient {base_currency} balance ({base_balance:.6f}) for SELL order (${order_value:.2f})"
                        }
                    else:
                        # Adjust amount to meet minimum order value
                        adjusted_amount = min_order_value_usd / current_price
                        if base_balance >= adjusted_amount:
                            return {
                                'strategy': 'sell_adjusted',
                                'side': 'sell',
                                'amount': adjusted_amount,
                                'symbol': symbol,
                                'reason': f"Adjusted SELL amount to meet $10 minimum: {adjusted_amount:.6f} {base_currency} (${min_order_value_usd:.2f})"
                            }
                        else:
                            return {
                                'strategy': 'insufficient_crypto',
                                'error': f"Insufficient {base_currency} balance ({base_balance:.6f}) for minimum $10 SELL order (need {adjusted_amount:.6f})"
                            }
                else:
                    return {
                        'strategy': 'insufficient_crypto',
                        'error': f"Insufficient {base_currency} balance ({base_balance:.6f}) for SELL amount ({amount:.6f})"
                    }

                return {
                    'strategy': 'insufficient_crypto',
                    'error': f"Insufficient {base_currency} balance ({base_balance:.6f}) for SELL order ({amount:.6f})"
                }

        except Exception as e:
            logger.error(f"Error determining optimal order strategy: {e}")
            return {
                'strategy': 'error',
                'error': f"Strategy determination failed: {str(e)}"
            }

    async def validate_order_requirements(self, symbol: str, quantity: float, price: Optional[float] = None, side: str = 'buy') -> dict:
        """Validate order against Bybit's minimum requirements"""
        try:
            requirements = self._get_bybit_minimum_requirements(symbol)
            validation_result = {
                'valid': True,
                'errors': [],
                'warnings': [],
                'adjusted_quantity': quantity,
                'adjusted_price': price,
                'order_value_usdt': 0.0
            }

            # Get current price if not provided
            if price is None:
                price = float(self.get_price(symbol))
                if price <= 0:
                    validation_result['valid'] = False
                    validation_result['errors'].append(f"Could not get current price for {symbol}")
                    return validation_result

            # Calculate order value in USDT
            order_value = quantity * price
            validation_result['order_value_usdt'] = order_value

            # Check minimum order value
            min_value = requirements['min_order_value_usdt']
            # CRITICAL FIX: Use smaller buffer for aggressive micro-trading
            min_value_with_buffer = min_value * 1.1  # Use 10% buffer instead of 100%
            if order_value < min_value_with_buffer:
                # Auto-adjust quantity to meet minimum with buffer
                adjusted_quantity = min_value_with_buffer / price

                # CRITICAL FIX: Handle BUY vs SELL orders differently
                if side.lower() == 'sell':
                    # For SELL orders, check if we have enough of the base currency
                    base_currency = symbol.replace('USDT', '').replace('USD', '')

                    # Get available balance for the base currency
                    try:
                        # CRITICAL FIX: Get REAL-TIME balance instead of hardcoded fallbacks
                        available_balance = 0.0
                        try:
                            # Force fresh balance fetch from API
                            balance_decimal = await self.get_balance(base_currency)
                            available_balance = float(balance_decimal)
                            logger.info(f"🔄 [REAL-TIME-VALIDATION] {base_currency} balance: {available_balance:.6f} (fresh from API)")
                        except Exception as balance_error:
                            logger.error(f"❌ [BALANCE-FETCH] Failed to get real-time {base_currency} balance: {balance_error}")
                            # CRITICAL FIX: Fail-fast instead of using cached data
                            validation_result['valid'] = False
                            validation_result['errors'].append(
                                f"Real-time balance data unavailable from Bybit API for {base_currency}: {balance_error}"
                            )
                            return validation_result

                        # CRITICAL FIX: Never exceed available balance - use 85% for safety
                        max_sellable = available_balance * 0.85

                        if adjusted_quantity > max_sellable:
                            # CRITICAL FIX: Always adjust to maximum sellable amount instead of failing
                            if max_sellable > 0:
                                adjusted_quantity = max_sellable
                                logger.warning(f"🔧 [SELL-ADJUST] Adjusted sell quantity to max available: {adjusted_quantity:.6f} {base_currency}")

                                # Check if adjusted amount meets minimum order value
                                adjusted_order_value = adjusted_quantity * price
                                if adjusted_order_value < min_value:
                                    # If still below minimum, try to use minimum viable amount
                                    min_viable_quantity = min_value / price
                                    if min_viable_quantity <= max_sellable:
                                        adjusted_quantity = min_viable_quantity
                                        logger.warning(f"🔧 [SELL-ADJUST] Using minimum viable quantity: {adjusted_quantity:.6f} {base_currency}")
                                    else:
                                        # Only fail if we absolutely cannot meet minimum requirements
                                        validation_result['valid'] = False
                                        validation_result['errors'].append(
                                            f"Cannot meet minimum order value: need ${min_value:.2f}, max possible: ${adjusted_order_value:.2f}"
                                        )
                                        return validation_result
                            else:
                                validation_result['valid'] = False
                                validation_result['errors'].append(
                                    f"No {base_currency} balance available for selling"
                                )
                                return validation_result

                        # Also check original quantity doesn't exceed balance
                        if quantity > max_sellable:
                            logger.warning(f"🔧 [SELL-ADJUST] Original quantity {quantity:.6f} > max sellable {max_sellable:.6f}, using adjusted amount")

                    except Exception as e:
                        logger.warning(f"Could not check {base_currency} balance: {e}")

                elif side.lower() == 'buy':
                    # For BUY orders, check if we have enough USDT for the adjusted order value
                    adjusted_order_value = adjusted_quantity * price
                    try:
                        # CRITICAL FIX: Get REAL-TIME USDT balance instead of hardcoded fallback
                        usdt_balance = 0.0
                        try:
                            # Force fresh USDT balance fetch from API
                            balance_decimal = await self.get_balance('USDT')
                            usdt_balance = float(balance_decimal)
                            logger.info(f"🔄 [REAL-TIME-VALIDATION] USDT balance: ${usdt_balance:.2f} (fresh from API)")
                        except Exception as balance_error:
                            logger.error(f"❌ [BALANCE-FETCH] Failed to get real-time USDT balance: {balance_error}")
                            # CRITICAL FIX: Fail-fast instead of using cached data
                            validation_result['valid'] = False
                            validation_result['errors'].append(
                                f"Real-time USDT balance data unavailable from Bybit API: {balance_error}"
                            )
                            return validation_result

                        # CRITICAL FIX: Never exceed available USDT balance - use 85% for safety
                        max_spendable = usdt_balance * 0.85

                        if adjusted_order_value > max_spendable:
                            # Instead of failing, adjust to maximum spendable amount
                            if max_spendable >= min_value:
                                adjusted_quantity = max_spendable / price
                                adjusted_order_value = max_spendable
                                logger.warning(f"🔧 [BUY-ADJUST] Adjusted buy value to max available: ${adjusted_order_value:.2f}")
                            else:
                                validation_result['valid'] = False
                                validation_result['errors'].append(
                                    f"Insufficient USDT balance: need ${adjusted_order_value:.2f}, have ${usdt_balance:.2f} (max spendable: ${max_spendable:.2f})"
                                )
                                return validation_result

                        # Also check original order value doesn't exceed balance
                        original_order_value = quantity * price
                        if original_order_value > max_spendable:
                            logger.info(f"✅ [BUY-OPTIMIZE] Original order value ${original_order_value:.2f} > max spendable ${max_spendable:.2f}, using optimized amount")

                    except Exception as e:
                        logger.warning(f"Could not check USDT balance: {e}")

                validation_result['adjusted_quantity'] = adjusted_quantity
                validation_result['warnings'].append(
                    f"Order value ${order_value:.2f} below minimum ${min_value:.2f}. "
                    f"Adjusted quantity from {quantity:.6f} to {adjusted_quantity:.6f}"
                )
                quantity = adjusted_quantity
                order_value = quantity * price

            # Check minimum quantity
            min_qty = requirements['min_qty']
            if quantity < min_qty:
                validation_result['adjusted_quantity'] = min_qty
                validation_result['warnings'].append(
                    f"Quantity {quantity:.6f} below minimum {min_qty:.6f}. Adjusted to {min_qty:.6f}"
                )
                quantity = min_qty
                order_value = quantity * price

            # CRITICAL FIX: Use Decimal arithmetic for precise quantity rounding to prevent boundary condition bugs
            from decimal import Decimal, ROUND_HALF_UP
            qty_precision = requirements['qty_precision']

            # Convert to Decimal for precise arithmetic
            quantity_decimal = Decimal(str(quantity))
            price_decimal = Decimal(str(price))
            min_value_decimal = Decimal(str(min_value))

            # Round quantity using Decimal precision
            rounded_quantity_decimal = quantity_decimal.quantize(
                Decimal('0.1') ** qty_precision,
                rounding=ROUND_HALF_UP
            )
            rounded_quantity = float(rounded_quantity_decimal)

            if rounded_quantity != quantity:
                validation_result['adjusted_quantity'] = rounded_quantity
                validation_result['warnings'].append(
                    f"Rounded quantity from {quantity:.8f} to {rounded_quantity:.8f} "
                    f"(precision: {qty_precision} decimals)"
                )

            # CRITICAL FIX: Use Decimal arithmetic for final order value calculation
            final_order_value_decimal = rounded_quantity_decimal * price_decimal
            final_order_value = float(final_order_value_decimal)

            # CRITICAL FIX: Add comprehensive debug logging before validation checks
            logger.info(f"🔍 [ORDER-VALIDATION] Order value: ${final_order_value:.6f}")
            logger.info(f"🔍 [ORDER-VALIDATION] Minimum required: ${min_value:.6f}")
            logger.info(f"🔍 [ORDER-VALIDATION] Decimal comparison: {final_order_value_decimal} >= {min_value_decimal}")
            logger.info(f"🔍 [ORDER-VALIDATION] Validation result: {'PASS' if final_order_value_decimal >= min_value_decimal else 'FAIL'}")

            # CRITICAL FIX: Use Decimal comparison to prevent floating-point precision issues
            # Use the original minimum (without buffer) for final validation since we already adjusted with buffer
            if final_order_value_decimal < min_value_decimal:
                validation_result['valid'] = False
                validation_result['errors'].append(
                    f"Final order value ${final_order_value:.2f} still below minimum ${min_value:.2f}"
                )

            validation_result['order_value_usdt'] = final_order_value

            return validation_result

        except Exception as e:
            logger.error(f"Error validating order requirements for {symbol}: {e}")
            return {
                'valid': False,
                'errors': [f"Validation error: {str(e)}"],
                'warnings': [],
                'adjusted_quantity': quantity,
                'adjusted_price': price,
                'order_value_usdt': 0.0
            }

    def _round_amount_for_symbol(self, symbol: str, amount: float) -> float:
        """FIXED: Round amount to appropriate decimal precision and enforce Bybit minimums"""
        try:
            requirements = self._get_bybit_minimum_requirements(symbol)

            # CRITICAL FIX: Use proper precision to prevent "too many decimals" error
            precision = requirements['qty_precision']

            # Use Decimal for precise rounding
            from decimal import Decimal, ROUND_HALF_UP
            amount_decimal = Decimal(str(amount))

            # Round to exact precision required by Bybit
            rounded_decimal = amount_decimal.quantize(
                Decimal('0.1') ** precision,
                rounding=ROUND_HALF_UP
            )

            rounded = float(rounded_decimal)

            # Enforce minimum quantity
            min_qty = requirements['min_qty']
            if rounded < min_qty:
                # Round minimum quantity to same precision
                min_qty_decimal = Decimal(str(min_qty))
                min_qty_rounded = min_qty_decimal.quantize(
                    Decimal('0.1') ** precision,
                    rounding=ROUND_HALF_UP
                )
                rounded = float(min_qty_rounded)
                logger.info(f"✅ [BYBIT-OPTIMIZE] {symbol}: Optimized quantity from {amount:.{precision}f} to {rounded:.{precision}f} (minimum required)")

            logger.debug(f"[BYBIT-PRECISION] {symbol}: {amount:.8f} -> {rounded:.{precision}f} (precision: {precision})")
            return rounded

        except Exception as e:
            logger.error(f"Error rounding amount for {symbol}: {e}")
            return max(round(amount, 4), 0.01)  # Fallback with 4 decimal precision

    def get_price(self, symbol: str) -> Decimal:
        """Get the current market price for a symbol with enhanced error handling, timestamp sync, and performance optimization."""
        start_time = None
        if performance_optimizer:
            start_time = performance_optimizer.start_operation_timer()

        try:
            # REAL EXCHANGE VALIDATION: Only reject symbols confirmed invalid by Bybit API
            normalized_symbol = self._normalize_symbol(symbol)
            logger.debug(f"Getting price for {symbol} (normalized to {normalized_symbol})")

            # Check if symbol is valid using real exchange data
            if not self._is_valid_bybit_symbol(normalized_symbol):
                logger.warning(f"❌ [EXCHANGE-INVALID] Symbol not found on Bybit exchange: {normalized_symbol}")
                raise ValueError(f"Symbol not available on Bybit: {normalized_symbol}")

            # Check cache first
            if performance_optimizer:
                cached_price = performance_optimizer.get_cached_data('price', normalized_symbol)
                if cached_price is not None:
                    logger.debug(f"[PRICE-CACHE] Using cached price for {normalized_symbol}: {cached_price}")
                    return Decimal(str(cached_price))

            # ENTERPRISE-GRADE: Use professional retry mechanism for price queries
            if self.session:
                api_start = time.time()
                response = self._execute_with_enterprise_retry(
                    self.session.get_tickers,
                    category="spot",
                    symbol=normalized_symbol
                )
                api_time = (time.time() - api_start) * 1000

                if performance_optimizer:
                    performance_optimizer.record_api_response_time(api_time)

                if response and response["retCode"] == 0 and response["result"]["list"]:
                    price = Decimal(response["result"]["list"][0]["lastPrice"])

                    # Cache the result
                    if performance_optimizer:
                        performance_optimizer.set_cached_data('price', normalized_symbol, str(price))
                        performance_optimizer.record_operation_result(True)

                    return price

                logger.warning(f"Bybit Fixed: Could not get price for {normalized_symbol}. Response: {response}")

                if performance_optimizer:
                    performance_optimizer.record_operation_result(False)

                # Raise exception instead of returning 0.0 for invalid symbols
                raise ValueError(f"Could not get price for {normalized_symbol}: Invalid response from exchange")

        except ValueError as ve:
            # Re-raise ValueError (symbol validation errors)
            if performance_optimizer:
                performance_optimizer.record_operation_result(False)
            raise ve
        except Exception as e:
            logger.error(f"Bybit Fixed: Error getting price for {symbol}: {e}")

            if performance_optimizer:
                performance_optimizer.record_operation_result(False)

            # Raise exception instead of returning 0.0 for API errors
            raise RuntimeError(f"Failed to get price for {symbol}: {e}")
        finally:
            if performance_optimizer and start_time:
                performance_optimizer.end_operation_timer(start_time, "price_fetch")

    async def get_fast_price(self, symbol: str) -> float:
        """ASYNC: Get current market price for aggressive trading"""
        try:
            price = self.get_price_fast(symbol)
            return float(price) if price else 0.0
        except Exception as e:
            logger.error(f"❌ [FAST-PRICE] Error getting price for {symbol}: {e}")
            return 0.0

    def get_price_fast(self, symbol: str) -> Decimal:
        """FAST & ACCURATE: Get current market price with minimal overhead (< 50ms)"""
        try:
            # FAST PATH: Simple normalization and direct API call
            normalized_symbol = self._normalize_symbol(symbol)

            # FAST CACHE CHECK: Simple in-memory cache
            cache_key = f"price_{normalized_symbol}"
            current_time = time.time()

            if hasattr(self, '_fast_price_cache') and cache_key in self._fast_price_cache:
                cached_data, cache_time = self._fast_price_cache[cache_key]
                if current_time - cache_time < 3:  # 3 second cache for speed
                    return Decimal(str(cached_data))

            # FAST API CALL: Direct session call without enterprise overhead
            response = self.session.get_tickers(category="spot", symbol=normalized_symbol)

            if response and response["retCode"] == 0 and response["result"]["list"]:
                price = Decimal(response["result"]["list"][0]["lastPrice"])

                # FAST CACHE: Simple cache storage
                if not hasattr(self, '_fast_price_cache'):
                    self._fast_price_cache = {}
                self._fast_price_cache[cache_key] = (str(price), current_time)

                # Clean old cache entries (keep only last 50 for speed)
                if len(self._fast_price_cache) > 50:
                    oldest_keys = sorted(self._fast_price_cache.keys(),
                                       key=lambda k: self._fast_price_cache[k][1])[:25]
                    for old_key in oldest_keys:
                        del self._fast_price_cache[old_key]

                return price
            else:
                logger.warning(f"❌ [FAST-PRICE] Invalid response for {normalized_symbol}")
                raise RuntimeError(f"Invalid API response for {normalized_symbol}")

        except Exception as e:
            logger.error(f"❌ [FAST-PRICE] Error getting price for {symbol}: {e}")
            raise RuntimeError(f"Failed to get price for {symbol}: {e}")

    # OPTIMIZED: Ultra-fast price methods using performance optimizations
    @cached_market_data(ttl_seconds=3.0)
    async def get_price_optimized(self, symbol: str) -> Decimal:
        """ULTRA-OPTIMIZED: Get current price using connection pool with intelligent caching"""
        if not PERFORMANCE_OPTIMIZATIONS_AVAILABLE:
            return self.get_price_fast(symbol)

        try:
            normalized_symbol = self._normalize_symbol(symbol)

            # Check intelligent cache first
            cache_key = f"price_{normalized_symbol}"
            if hasattr(self, '_price_cache') and cache_key in self._price_cache:
                cache_time, price = self._price_cache[cache_key]
                if time.time() - cache_time < self._cache_ttl:
                    return price

            # Use high-speed connection pool for API call
            start_time = time.time()

            # Direct API call using optimized connection pool
            url = f"{self.base_url}/v5/market/tickers?category=spot&symbol={normalized_symbol}"
            headers = self._get_headers()

            response = await connection_pool.make_request("GET", url, headers, endpoint="tickers")

            execution_time = (time.time() - start_time) * 1000

            if response.get('retCode') == 0 and response.get('result', {}).get('list'):
                price = Decimal(response['result']['list'][0]['lastPrice'])
                # Cache the result
                if hasattr(self, '_price_cache'):
                    self._price_cache[cache_key] = (time.time(), price)

                # Log performance if slow
                if execution_time > 300:
                    logger.warning(f"⚠️ [OPTIMIZED] Slow price fetch: {execution_time:.1f}ms for {symbol}")
                else:
                    logger.debug(f"🚀 [OPTIMIZED] Fast price fetch: {execution_time:.1f}ms for {symbol}")

                return price
            else:
                logger.warning(f"Failed to get optimized price for {normalized_symbol}: {response}")
                return Decimal('0')

        except Exception as e:
            logger.error(f"Error getting optimized price for {symbol}: {e}")
            # Fallback to regular fast method
            return self.get_price_fast(symbol)

    @smart_cache(category="price", ttl=2.0, priority=3)
    async def get_multiple_prices_optimized(self, symbols: list) -> Dict[str, Decimal]:
        """ULTRA-OPTIMIZED: Get multiple prices in parallel using bulk requests"""
        if not PERFORMANCE_OPTIMIZATIONS_AVAILABLE:
            return {symbol: self.get_price_fast(symbol) for symbol in symbols}

        try:
            # Prepare bulk requests
            requests = []
            for symbol in symbols:
                normalized_symbol = self._normalize_symbol(symbol)
                requests.append({
                    'method': 'GET',
                    'url': f"{self.base_url}/v5/market/tickers?category=spot&symbol={normalized_symbol}",
                    'headers': self._get_headers(),
                    'endpoint': f'tickers_{normalized_symbol}'
                })

            # Execute bulk request using connection pool
            start_time = time.time()
            results = await connection_pool.bulk_request(requests, max_concurrent=10)
            execution_time = (time.time() - start_time) * 1000

            # Process results
            prices = {}
            for i, result in enumerate(results):
                symbol = symbols[i]
                if isinstance(result, dict) and not result.get('error'):
                    if result.get('retCode') == 0 and result.get('result', {}).get('list'):
                        prices[symbol] = Decimal(result['result']['list'][0]['lastPrice'])
                    else:
                        prices[symbol] = Decimal('0')
                else:
                    prices[symbol] = Decimal('0')
                    logger.warning(f"Failed to get bulk price for {symbol}: {result}")

            logger.info(f"🚀 [BULK-OPTIMIZED] Fetched {len(symbols)} prices in {execution_time:.1f}ms")
            return prices

        except Exception as e:
            logger.error(f"Error getting multiple optimized prices: {e}")
            # Fallback to individual calls
            return {symbol: self.get_price_fast(symbol) for symbol in symbols}

    def _get_headers(self) -> Dict[str, str]:
        """Get headers for direct API calls"""
        timestamp = str(int(time.time() * 1000))
        return {
            'X-BAPI-API-KEY': self.api_key,
            'X-BAPI-TIMESTAMP': timestamp,
            'X-BAPI-RECV-WINDOW': str(self.recv_window),
            'Content-Type': 'application/json'
        }

    def get_market_state(self, symbol: str) -> dict:
        """Get a comprehensive market state with fallbacks."""
        try:
            normalized_symbol = self._normalize_symbol(symbol)
            response = self.session.get_tickers(
                category="spot", symbol=normalized_symbol
            )
            if response and response["retCode"] == 0 and response["result"]["list"]:
                ticker_data = response["result"]["list"][0]
                return {
                    "price": Decimal(ticker_data.get("lastPrice", "0")),
                    "bid": Decimal(ticker_data.get("bid1Price", "0")),
                    "ask": Decimal(ticker_data.get("ask1Price", "0")),
                    "volume": Decimal(ticker_data.get("volume24h", "0")),
                    "high_24h": Decimal(ticker_data.get("highPrice24h", "0")),
                    "low_24h": Decimal(ticker_data.get("lowPrice24h", "0")),
                    "open_24h": Decimal(ticker_data.get("prevPrice24h", "0")),
                    "last_trade_size": Decimal(ticker_data.get("lastTradeQty", "0")),
                    "status": "active",
                    "exchange": self.name
                }
            return {"status": "inactive", "exchange": self.name}
        except Exception as e:
            logger.error(f"Bybit Fixed: Error getting market state for {symbol}: {e}")
            return {"status": "error", "exchange": self.name, "error": str(e)}

    def _check_rate_limit(self, endpoint_type: str = 'default') -> bool:
        """Check if request is within rate limits"""
        current_time = time.time()
        limit_config = self.rate_limits.get(endpoint_type, self.rate_limits['default'])
        window = limit_config['window']
        max_requests = limit_config['requests']

        # Clean old requests outside the window
        request_times = self.rate_limiter[endpoint_type]
        while request_times and current_time - request_times[0] > window:
            request_times.popleft()

        # Check if we're within limits
        if len(request_times) >= max_requests:
            sleep_time = window - (current_time - request_times[0])
            if sleep_time > 0:
                logger.warning(f"⚠️ [RATE-LIMIT] {endpoint_type} rate limit reached, sleeping {sleep_time:.2f}s")
                time.sleep(sleep_time)
                return self._check_rate_limit(endpoint_type)  # Recheck after sleep

        # Add current request
        request_times.append(current_time)
        return True

    async def get_balance(self, coin: str = "USDT") -> Decimal:
        """Get account balance with enhanced error handling and timestamp sync - ASYNC FIXED."""
        # Apply rate limiting
        self._check_rate_limit('balance')

        try:
            # CRITICAL FIX: Bybit only supports UNIFIED account type now
            total_balance = Decimal("0")

            try:
                # ENTERPRISE-GRADE: Use professional retry mechanism for balance queries
                if self.session:
                    response = self._execute_with_enterprise_retry(
                        self.session.get_wallet_balance,
                        accountType=self.account_manager.get_primary_account_type(),
                        coin=coin
                    )

                    # CRITICAL DEBUG: Log the actual API response for troubleshooting
                    logger.info(f"🔍 [BALANCE-DEBUG] API Response for {coin}: retCode={response.get('retCode') if response else 'None'}")
                    if response:
                        logger.info(f"🔍 [BALANCE-DEBUG] Response keys: {list(response.keys())}")
                        # CRITICAL FIX: Add comprehensive response structure debugging
                        import json
                        logger.info(f"🔍 [BALANCE-DEBUG] Full API response structure: {json.dumps(response, indent=2, default=str)}")

                        if response.get('retCode') != 0:
                            logger.error(f"❌ [BALANCE-API] Bybit API error: {response.get('retMsg', 'Unknown error')}")
                            logger.error(f"❌ [BALANCE-API] Full response: {response}")
                            # CRITICAL: Don't return 0, raise the actual error
                            raise Exception(f"Bybit API error {response.get('retCode')}: {response.get('retMsg', 'Unknown error')}")

                    if response and response["retCode"] == 0:
                        # CRITICAL FIX: Enhanced response structure parsing
                        result = response.get("result", {})
                        logger.info(f"🔍 [BALANCE-DEBUG] Result structure: {json.dumps(result, indent=2, default=str)}")

                        account_list = result.get("list", [])
                        if not account_list:
                            logger.warning(f"⚠️ [BALANCE-DEBUG] No accounts found in response")
                            return Decimal("0")

                        logger.info(f"🔍 [BALANCE-DEBUG] Found {len(account_list)} accounts")

                        for account_idx, account in enumerate(account_list):
                            logger.info(f"🔍 [BALANCE-DEBUG] Account {account_idx}: {json.dumps(account, indent=2, default=str)}")

                            coin_list = account.get("coin", [])
                            logger.info(f"🔍 [BALANCE-DEBUG] Account {account_idx} has {len(coin_list)} coins")

                            for coin_idx, c in enumerate(coin_list):
                                coin_symbol = c.get("coin", "")
                                wallet_balance = c.get("walletBalance", "0")
                                logger.info(f"🔍 [BALANCE-DEBUG] Coin {coin_idx}: {coin_symbol} = {wallet_balance}")

                                if coin_symbol == coin:
                                    balance = Decimal(wallet_balance)
                                    total_balance += balance
                                    logger.info(f"✅ [BALANCE-SUCCESS] Found {coin} balance in UNIFIED: {balance}")

                                    # CRITICAL: Validate balance data with real money trading validator
                                    try:
                                        from src.validation.real_money_trading_validator import real_money_validator
                                        balance_valid = await real_money_validator.validate_balance_data(
                                            exchange=self.name,
                                            currency=coin,
                                            balance=float(total_balance)
                                        )
                                        if not balance_valid:
                                            error_msg = f"CRITICAL: Balance validation failed for {coin}: {total_balance}"
                                            logger.error(f"❌ [BALANCE-VALIDATION] {error_msg}")
                                            raise Exception(error_msg)
                                    except Exception as validation_error:
                                        error_msg = f"CRITICAL: Balance validation error for {coin}: {validation_error}"
                                        logger.error(f"❌ [BALANCE-VALIDATION] {error_msg}")
                                        raise Exception(error_msg)

                                    return total_balance  # Return immediately when found

                        # If we get here, the coin wasn't found in the response
                        available_coins = [c.get('coin') for account in account_list for c in account.get('coin', [])]
                        logger.warning(f"⚠️ [BALANCE-NOT-FOUND] {coin} not found in account. Available coins: {available_coins}")
                        logger.warning(f"⚠️ [BALANCE-NOT-FOUND] Returning 0 balance for {coin}")
                        return Decimal("0.0")
                    else:
                        logger.error(f"❌ [BALANCE-API] Invalid response structure: {response}")
                        raise Exception(f"Invalid Bybit API response for {coin} balance")

            except Exception as e:
                logger.error(f"❌ [BALANCE-FETCH] Failed to get {coin} balance: {e}")
                # CRITICAL: Don't silently return 0, propagate the error for debugging
                raise Exception(f"Real-time balance data unavailable from Bybit API for {coin}: {e}")

            return total_balance
        except Exception as e:
            logger.error(f"❌ [BALANCE-ERROR] Critical error getting balance for {coin}: {e}")
            # CRITICAL: Fail-fast instead of returning 0 to maintain zero-tolerance policy
            raise Exception(f"Real-time balance data unavailable: {e}")

    # OPTIMIZED: Ultra-fast balance method using performance optimizations
    @cached_market_data(ttl_seconds=2.0)
    async def get_balance_optimized(self, coin: str = "USDT") -> Decimal:
        """ULTRA-OPTIMIZED: Get account balance using connection pool with intelligent caching"""
        if not PERFORMANCE_OPTIMIZATIONS_AVAILABLE:
            return await self.get_balance(coin)

        try:
            # Check intelligent cache first
            cache_key = f"balance_{coin}"
            if hasattr(self, '_balance_cache') and cache_key in self._balance_cache:
                cache_time, balance = self._balance_cache[cache_key]
                if time.time() - cache_time < self._cache_ttl:
                    return balance

            # Use high-speed connection pool for API call
            start_time = time.time()

            # Direct API call using optimized connection pool
            url = f"{self.base_url}/v5/account/wallet-balance"
            headers = self._get_headers()
            params = {
                "accountType": "UNIFIED",
                "coin": coin
            }

            response = await connection_pool.make_request("GET", url, headers, params=params, endpoint="wallet-balance")

            execution_time = (time.time() - start_time) * 1000

            if response.get('retCode') == 0 and response.get('result', {}).get('list'):
                for account in response['result']['list']:
                    for c in account.get('coin', []):
                        if c['coin'] == coin:
                            balance = Decimal(c['walletBalance'])
                            # Cache the result
                            if hasattr(self, '_balance_cache'):
                                self._balance_cache[cache_key] = (time.time(), balance)

                            # Log performance if slow
                            if execution_time > 100:
                                logger.warning(f"⚠️ [OPTIMIZED] Slow balance fetch: {execution_time:.1f}ms for {coin}")
                            else:
                                logger.debug(f"🚀 [OPTIMIZED] Fast balance fetch: {execution_time:.1f}ms for {coin}")

                            return balance

            # Cache zero balance to avoid repeated API calls
            if hasattr(self, '_balance_cache'):
                self._balance_cache[cache_key] = (time.time(), Decimal('0'))
            return Decimal('0')

        except Exception as e:
            logger.error(f"Error getting optimized balance for {coin}: {e}")
            # Fallback to regular method
            return await self.get_balance(coin)

    def get_all_balances(self) -> dict:
        """Get all account balances from UNIFIED account type only."""
        all_balances = {}

        try:
            logger.info("Checking UNIFIED account balances...")

            # ENTERPRISE-GRADE: Use professional retry mechanism for balance queries
            if self.session:
                response = self._execute_with_enterprise_retry(
                    self.session.get_wallet_balance,
                    accountType=self.account_manager.get_primary_account_type()
                )

                if response and response["retCode"] == 0 and response["result"]["list"]:
                    for account in response["result"]["list"]:
                        for coin_data in account.get("coin", []):
                            coin = coin_data["coin"]

                            # ENTERPRISE-GRADE: Safe decimal conversion with comprehensive error handling
                            try:
                                # Get raw values with safe defaults
                                wallet_balance_raw = coin_data.get("walletBalance", "0")
                                equity_raw = coin_data.get("equity", "0")
                                available_raw = coin_data.get("availableToWithdraw", "0")
                                locked_raw = coin_data.get("locked", "0")

                                # Handle empty strings and None values
                                wallet_balance_str = str(wallet_balance_raw) if wallet_balance_raw not in [None, "", "null"] else "0"
                                equity_str = str(equity_raw) if equity_raw not in [None, "", "null"] else "0"
                                available_str = str(available_raw) if available_raw not in [None, "", "null"] else "0"
                                locked_str = str(locked_raw) if locked_raw not in [None, "", "null"] else "0"

                                # Clean strings (remove any non-numeric characters except decimal point and minus)
                                import re
                                wallet_balance_clean = re.sub(r'[^\d.-]', '', wallet_balance_str)
                                equity_clean = re.sub(r'[^\d.-]', '', equity_str)
                                available_clean = re.sub(r'[^\d.-]', '', available_str)
                                locked_clean = re.sub(r'[^\d.-]', '', locked_str)

                                # Convert to Decimal with fallback
                                wallet_balance = Decimal(wallet_balance_clean) if wallet_balance_clean else Decimal("0")
                                equity = Decimal(equity_clean) if equity_clean else Decimal("0")
                                available = Decimal(available_clean) if available_clean else Decimal("0")
                                locked = Decimal(locked_clean) if locked_clean else Decimal("0")

                            except (ValueError, TypeError, decimal.InvalidOperation) as e:
                                logger.warning(f"[BALANCE-PARSE] Error parsing {coin} balance data: {e}")
                                logger.warning(f"[BALANCE-PARSE] Raw data: wallet={wallet_balance_raw}, equity={equity_raw}, available={available_raw}")
                                continue

                            if wallet_balance > 0 or equity > 0 or available > 0:
                                all_balances[f"{coin}_UNIFIED"] = {
                                    "coin": coin,
                                    "account_type": "UNIFIED",
                                    "wallet_balance": wallet_balance,
                                    "equity": equity,
                                    "available": available,
                                    "used": locked,
                                    "free": available
                                }
                                logger.info(f"Found {coin} balance in UNIFIED: {wallet_balance}")
                else:
                    logger.info("No balances found in UNIFIED account")

        except Exception as e:
            logger.warning(f"Error checking UNIFIED account: {e}")

        return all_balances

    def fetch_account(self) -> dict:
        """Fetch comprehensive account information including all balances."""
        try:
            # Get account info
            account_info = self.session.get_account_info()
            
            # Get all balances
            balances = self.get_all_balances()
            
            return {
                "account_info": account_info,
                "balances": balances,
                "exchange": self.name,
                "timestamp": account_info.get("time", 0) if account_info else 0
            }
        except Exception as e:
            logger.error(f"Bybit Fixed: Error fetching account: {e}")
            return {"balances": {}, "exchange": self.name, "error": str(e)}

    def fetch_balance(self) -> dict:
        """Fetch balance in ccxt-compatible format."""
        try:
            all_balances = self.get_all_balances()
            
            # Convert to ccxt format
            free = {}
            used = {}
            total = {}
            
            for balance_key, balance_data in all_balances.items():
                coin = balance_data["coin"]
                
                # Aggregate across account types for same coin
                if coin not in free:
                    free[coin] = Decimal("0")
                    used[coin] = Decimal("0")
                    total[coin] = Decimal("0")
                
                free[coin] += balance_data["free"]
                used[coin] += balance_data["used"]
                total[coin] += balance_data["wallet_balance"]
            
            return {
                "free": {k: float(v) for k, v in free.items()},
                "used": {k: float(v) for k, v in used.items()},
                "total": {k: float(v) for k, v in total.items()},
            }
        except Exception as e:
            logger.error(f"Bybit Fixed: Error fetching balance: {e}")
            return {"free": {}, "used": {}, "total": {}}

    async def place_order(self, symbol: str, side: str, amount: float, price: Optional[float] = None,
                         order_type: str = "market", time_in_force: str = "GTC", is_quote_amount: bool = False) -> dict:
        """Place a trading order with comprehensive Bybit validation and compliance"""
        trade_id = None
        start_time = time.time()

        try:
            normalized_symbol = self._normalize_symbol(symbol)
            logger.info(f"🔧 [BYBIT-ORDER] Placing {side} order: {amount} {normalized_symbol} (quote_amount: {is_quote_amount})")

            # Extract currency pair for logging
            base_currency, quote_currency = self.currency_manager.extract_base_quote_from_symbol(normalized_symbol)
            currency_pair = {'base': base_currency, 'quote': quote_currency}

            # Get current price for logging
            current_price = price
            if current_price is None:
                try:
                    current_price = float(self.get_price(normalized_symbol))
                except:
                    current_price = 0.0

            # Calculate total value
            if is_quote_amount:
                total_value = amount  # Amount is already in quote currency
            else:
                total_value = amount * current_price

            # Capture pre-trade balances for verification
            pre_trade_balances = {}
            if trade_logger:
                try:
                    all_balances = await self.get_all_available_balances()
                    pre_trade_balances = all_balances.copy()
                except Exception as e:
                    logger.warning(f"[TRADE-LOGGER] Could not capture pre-trade balances: {e}")

            # Start trade logging
            if trade_logger:
                trade_data = {
                    'exchange': self.name,
                    'symbol': normalized_symbol,
                    'side': side,
                    'order_type': order_type,
                    'quantity': amount,
                    'price': current_price,
                    'total_value': total_value,
                    'currency_pair': currency_pair,
                    'pre_trade_balances': pre_trade_balances,
                    'strategy': 'adaptive_multi_currency',
                    'reason': f"{'Quote' if is_quote_amount else 'Base'} amount order"
                }
                trade_id = trade_logger.log_trade_start(trade_data)

            # Get current price for validation
            current_price = price
            if current_price is None or current_price <= 0:
                # CRITICAL FIX: Try multiple methods to get current price
                try:
                    current_price = float(self.get_price(normalized_symbol))
                    logger.info(f"🔍 [PRICE] Got price from get_price(): {current_price}")
                except Exception as e:
                    logger.warning(f"⚠️ [PRICE] get_price() failed: {e}")
                    current_price = 0

                # If still no price, try ticker
                if current_price <= 0:
                    try:
                        ticker = self.get_ticker(normalized_symbol)
                        current_price = float(ticker.get('last', 0))
                        logger.info(f"🔍 [PRICE] Got price from ticker: {current_price}")
                    except Exception as e:
                        logger.warning(f"⚠️ [PRICE] ticker failed: {e}")
                        current_price = 0

                # CRITICAL REAL-MONEY TRADING REQUIREMENT: NO FALLBACK PRICES ALLOWED
                # System must fail completely rather than use fake/hardcoded data
                if current_price <= 0:
                    error_msg = f"CRITICAL: Real-time price data unavailable for {normalized_symbol}. " \
                               f"System refusing to use hardcoded/fallback prices for real money trading. " \
                               f"All price methods failed: get_price(), ticker(). " \
                               f"Real money trading requires live market data only."
                    logger.error(f"❌ [REAL-MONEY-SAFETY] {error_msg}")
                    return {"error": error_msg, "exchange": self.name}

            # CRITICAL: Validate price data with real money trading validator
            try:
                from src.validation.real_money_trading_validator import real_money_validator
                price_valid = await real_money_validator.validate_price_data(
                    symbol=normalized_symbol,
                    price=current_price,
                    timestamp=time.time()
                )
                if not price_valid:
                    error_msg = f"CRITICAL: Price validation failed for {normalized_symbol}: {current_price}"
                    logger.error(f"❌ [PRICE-VALIDATION] {error_msg}")
                    return {"error": error_msg, "exchange": self.name}
            except Exception as validation_error:
                error_msg = f"CRITICAL: Price validation error for {normalized_symbol}: {validation_error}"
                logger.error(f"❌ [PRICE-VALIDATION] {error_msg}")
                return {"error": error_msg, "exchange": self.name}

            # CRITICAL FIX: Determine optimal order strategy to prevent ErrCode 170140
            logger.info("🎯 [STRATEGY] Determining optimal order strategy to prevent minimum order value errors...")
            strategy_result = await self.determine_optimal_order_strategy(normalized_symbol, side, amount)

            if strategy_result['strategy'] == 'error':
                error_msg = f"Strategy determination failed: {strategy_result['error']}"
                logger.error(f"❌ [STRATEGY] {error_msg}")
                return {"error": error_msg, "exchange": self.name}

            elif strategy_result['strategy'] in ['insufficient_funds', 'insufficient_crypto']:
                error_msg = strategy_result['error']
                logger.error(f"❌ [STRATEGY] {error_msg}")
                return {"error": error_msg, "exchange": self.name}

            elif strategy_result['strategy'] in ['sell_crypto', 'sell_adjusted']:
                # Strategy switched to SELL order - update parameters
                original_side = side
                original_symbol = normalized_symbol
                original_amount = amount

                side = strategy_result['side']
                normalized_symbol = strategy_result['symbol']
                amount = strategy_result['amount']

                logger.info(f"🔄 [STRATEGY-SWITCH] {original_side} {original_amount} {original_symbol} -> {side} {amount:.6f} {normalized_symbol}")
                logger.info(f"🔄 [STRATEGY-SWITCH] Reason: {strategy_result['reason']}")

                # Update current price for new symbol if needed
                if normalized_symbol != original_symbol:
                    current_price = float(self.get_price(normalized_symbol))
                    if current_price <= 0:
                        return {"error": f"Could not get current price for {normalized_symbol}", "exchange": self.name}

            else:
                # Strategy approved original order
                logger.info(f"✅ [STRATEGY] {strategy_result['reason']}")

            # CRITICAL FIX: Pre-trade balance verification
            logger.info("🔍 [PRE-TRADE] Verifying sufficient balance before order placement...")
            balance_check = await self.verify_sufficient_balance(normalized_symbol, side, amount, current_price)

            if not balance_check['sufficient']:
                error_msg = f"Pre-trade balance check failed: {balance_check.get('error', 'Insufficient funds')}"
                logger.error(f"❌ [PRE-TRADE] {error_msg}")
                return {"error": error_msg, "exchange": self.name}

            logger.info(f"✅ [PRE-TRADE] Balance check passed: {balance_check['available_balance']:.6f} {balance_check['currency']} available")

            # ENTERPRISE-GRADE: Multi-currency quote amount handling with adaptive position sizing
            if is_quote_amount and order_type.lower() == "market" and side.lower() == "buy":
                base_currency, quote_currency = self.currency_manager.extract_base_quote_from_symbol(normalized_symbol)

                logger.info(f"[ADAPTIVE-SIZING] Processing ${amount:.2f} {quote_currency} order for {normalized_symbol}")
                logger.info(f"[ADAPTIVE-SIZING] Base: {base_currency}, Quote: {quote_currency}")

                # DYNAMIC POSITION SIZING: Get recommended trade size using adaptive balance management
                recommended_sizing = await self.get_recommended_trade_size(normalized_symbol, side, amount)

                # Use adaptive sizing if no specific amount provided or if adaptive is smaller (safer)
                if amount is None or recommended_sizing['recommended_size'] < amount:
                    amount = recommended_sizing['recommended_size']
                    logger.info(f"[ADAPTIVE-SIZING] Using adaptive position size: ${amount:.2f}")
                    logger.info(f"[ADAPTIVE-SIZING] Size source: {recommended_sizing['size_source']}")
                    logger.info(f"[ADAPTIVE-SIZING] Position percentage: {recommended_sizing.get('position_percentage', 'N/A')}%")
                else:
                    logger.info(f"[ADAPTIVE-SIZING] Using specified amount: ${amount:.2f} (within adaptive limits)")

                # CRITICAL FIX: Intelligent balance-aware order sizing
                min_quote_amount = 5.0  # Bybit minimum order value

                # CRITICAL FIX: Get real-time balance and validate before sizing
                available_balance = await self.get_balance('USDT')
                if available_balance > 0:
                    available_balance_float = float(available_balance)
                    logger.info(f"💰 [BALANCE-CHECK] Real-time USDT balance: ${available_balance_float:.2f}")

                    # CRITICAL FIX: Never exceed available balance
                    if amount > available_balance_float:
                        logger.info(f"✅ [ORDER-OPTIMIZE] Requested ${amount:.2f} > available ${available_balance_float:.2f}")
                        # Use 85% of available balance for safety
                        amount = available_balance_float * 0.85
                        logger.info(f"✅ [ORDER-OPTIMIZE] Optimized to 85% of balance: ${amount:.2f}")

                    # Ensure minimum order value
                    if amount < min_quote_amount:
                        if available_balance_float >= min_quote_amount:
                            amount = min_quote_amount
                            logger.info(f"✅ [MIN-OPTIMIZE] Optimized to minimum: ${amount:.2f}")
                        else:
                            logger.error(f"❌ [INSUFFICIENT-BALANCE] Available ${available_balance_float:.2f} < minimum ${min_quote_amount:.2f}")
                            return {"error": f"Insufficient USDT balance: ${available_balance_float:.2f} < ${min_quote_amount:.2f} minimum", "exchange": self.name}
                else:
                    logger.error(f"❌ [NO-BALANCE] No USDT balance available")
                    return {"error": "No USDT balance available for trading", "exchange": self.name}

                # MULTI-CURRENCY FIX: Use intelligent balance verification across all currencies
                balance_result = await self.find_sufficient_balance_for_trade(
                    normalized_symbol, side, amount, current_price
                )

                if not balance_result['sufficient']:
                    error_msg = balance_result.get('error', 'Insufficient balance across all currencies')
                    logger.error(f"[MULTI-CURRENCY] {error_msg}")
                    return {"error": error_msg, "exchange": self.name}

                # Log successful multi-currency balance verification
                used_currency = balance_result['currency']
                available_balance = balance_result['available_balance']
                conversion_needed = balance_result.get('conversion_needed', False)

                if conversion_needed:
                    logger.info(f"[MULTI-CURRENCY] Using {used_currency} balance with conversion: "
                               f"{available_balance:.6f} {used_currency} -> {quote_currency}")
                else:
                    logger.info(f"[MULTI-CURRENCY] Using direct {used_currency} balance: {available_balance:.6f}")

                logger.info(f"[ADAPTIVE-SIZING] Final order amount: ${amount:.2f} with {used_currency} balance")

                # CRITICAL FIX: Final safety check to prevent $20 vs $5.87 errors
                final_balance_check = await self.get_balance('USDT')
                if final_balance_check and float(final_balance_check) > 0:
                    final_balance_float = float(final_balance_check)
                    if amount > final_balance_float:
                        logger.error(f"❌ [FINAL-SAFETY] Order ${amount:.2f} > final balance ${final_balance_float:.2f}")
                        return {"error": f"Final safety check failed: order ${amount:.2f} exceeds balance ${final_balance_float:.2f}", "exchange": self.name}
                    logger.info(f"✅ [FINAL-SAFETY] Order ${amount:.2f} <= balance ${final_balance_float:.2f}")

                # Convert quote amount to base currency quantity for order placement
                base_quantity = amount / current_price

                # CRITICAL P1 FIX: Validate the converted order with proper await
                validation = await self.validate_order_requirements(normalized_symbol, base_quantity, current_price, side)

                if not validation['valid']:
                    error_msg = f"Order validation failed: {'; '.join(validation['errors'])}"
                    logger.error(f"[BYBIT-VALIDATION] {error_msg}")
                    return {"error": error_msg, "exchange": self.name}

                # Use validated quantity
                final_quantity = validation['adjusted_quantity']

                # Log validation optimizations
                for warning in validation['warnings']:
                    logger.info(f"✅ [BYBIT-OPTIMIZE] {warning}")

                logger.info(f"[BYBIT-CONVERT] ${amount:.2f} USDT -> {final_quantity:.6f} {normalized_symbol.replace('USDT', '')} "
                           f"(order value: ${validation['order_value_usdt']:.2f})")

                # CRITICAL FIX: Format quantity with conservative precision to avoid "too many decimals" error
                # Start with 3 decimals for safety
                qty_str = f"{final_quantity:.3f}".rstrip('0').rstrip('.')

                # Ensure we don't end up with empty string
                if not qty_str or qty_str == '':
                    qty_str = f"{final_quantity:.3f}"

                # CRITICAL FIX: Try LIMIT order instead of MARKET to avoid ErrCode 170140
                if order_type.lower() == "market":
                    order_params = {
                        "category": "spot",
                        "symbol": normalized_symbol,
                        "side": side.capitalize(),
                        "orderType": "Limit",
                        "qty": qty_str,
                        "price": str(current_price),  # Use current market price for limit order
                        "timeInForce": "IOC"  # Immediate or Cancel for quick execution
                    }
                else:
                    order_params = {
                        "category": "spot",
                        "symbol": normalized_symbol,
                        "side": side.capitalize(),
                        "orderType": order_type.capitalize(),
                        "qty": qty_str,
                        "timeInForce": time_in_force
                    }

            else:
                # ADAPTIVE POSITION SIZING: Standard base currency order with dynamic sizing
                logger.info(f"[ADAPTIVE-SIZING] Processing base currency order: {amount} {normalized_symbol}")

                # Get recommended trade size for base currency amounts
                recommended_sizing = await self.get_recommended_trade_size(normalized_symbol, side, amount)

                # Apply adaptive sizing if recommended size is smaller (safer)
                if recommended_sizing['recommended_size'] < amount:
                    amount = recommended_sizing['recommended_size']
                    logger.info(f"[ADAPTIVE-SIZING] Adjusted to adaptive size: {amount:.6f}")
                    logger.info(f"[ADAPTIVE-SIZING] Size source: {recommended_sizing['size_source']}")
                    logger.info(f"[ADAPTIVE-SIZING] Position percentage: {recommended_sizing.get('position_percentage', 'N/A')}%")
                else:
                    logger.info(f"[ADAPTIVE-SIZING] Using specified amount: {amount:.6f} (within adaptive limits)")

                # CRITICAL P1 FIX: Validate the order with proper await
                validation = await self.validate_order_requirements(normalized_symbol, amount, current_price, side)

                if not validation['valid']:
                    error_msg = f"Order validation failed: {'; '.join(validation['errors'])}"
                    logger.error(f"[BYBIT-VALIDATION] {error_msg}")
                    return {"error": error_msg, "exchange": self.name}

                # Use validated quantity
                final_quantity = validation['adjusted_quantity']

                # Log validation optimizations
                for warning in validation['warnings']:
                    logger.info(f"✅ [BYBIT-OPTIMIZE] {warning}")

                logger.info(f"[ADAPTIVE-SIZING] Final quantity: {final_quantity:.6f} "
                           f"(order value: ${validation['order_value_usdt']:.2f})")

                # CRITICAL FIX: Apply proper precision rounding to prevent "too many decimals" error
                final_quantity_rounded = self._round_amount_for_symbol(normalized_symbol, final_quantity)

                # Get precision requirements for formatting
                requirements = self._get_bybit_minimum_requirements(normalized_symbol)
                precision = requirements['qty_precision']

                # Format with exact precision required by Bybit
                qty_str = f"{final_quantity_rounded:.{precision}f}"

                logger.info(f"[BYBIT-PRECISION] Formatted quantity: {qty_str} (precision: {precision})")

                # CRITICAL FIX: Try LIMIT order instead of MARKET to avoid ErrCode 170140
                if order_type.lower() == "market":
                    order_params = {
                        "category": "spot",
                        "symbol": normalized_symbol,
                        "side": side.capitalize(),
                        "orderType": "Limit",
                        "qty": qty_str,
                        "price": str(current_price),  # Use current market price for limit order
                        "timeInForce": "IOC"  # Immediate or Cancel for quick execution
                    }
                else:
                    order_params = {
                        "category": "spot",
                        "symbol": normalized_symbol,
                        "side": side.capitalize(),
                        "orderType": order_type.capitalize(),
                        "qty": qty_str,
                        "timeInForce": time_in_force
                    }

            # Add price for limit orders
            if order_type.lower() == "limit" and price:
                order_params["price"] = str(price)

            logger.info(f"🔧 [BYBIT-ORDER] Final order params: {order_params}")

            # ENTERPRISE-GRADE: Use professional retry mechanism for order placement
            if not self.session:
                return {"error": "Bybit session not initialized", "exchange": self.name}

            # CRITICAL FIX: Fail-fast precision validation BEFORE making API calls
            start_time = time.time()

            # Get precision requirements for this symbol
            requirements = self._get_bybit_minimum_requirements(normalized_symbol)
            required_precision = requirements['qty_precision']

            # Validate quantity precision BEFORE sending order
            original_qty = float(order_params["qty"])

            # Check if quantity has too many decimals
            qty_str = f"{original_qty:.10f}".rstrip('0').rstrip('.')
            decimal_places = len(qty_str.split('.')[-1]) if '.' in qty_str else 0

            if decimal_places > required_precision:
                logger.warning(f"🔧 [FAIL-FAST] Quantity {original_qty} has {decimal_places} decimals, but {normalized_symbol} requires {required_precision}")

                # Apply correct precision immediately
                from decimal import Decimal, ROUND_HALF_UP
                qty_decimal = Decimal(str(original_qty))
                corrected_qty = qty_decimal.quantize(
                    Decimal('0.1') ** required_precision,
                    rounding=ROUND_HALF_UP
                )
                order_params["qty"] = f"{corrected_qty:.{required_precision}f}".rstrip('0').rstrip('.')

                logger.info(f"🔧 [FAIL-FAST] Corrected quantity: {original_qty:.6f} -> {order_params['qty']}")

            # CRITICAL FIX: Actual API call to Bybit with proper async/await
            try:
                logger.info(f"🚀 [BYBIT-API] Placing order on Bybit exchange...")
                logger.info(f"🚀 [BYBIT-API] Order params: {order_params}")

                # CRITICAL FIX: Use direct session call instead of retry wrapper for now
                response = self.session.place_order(**order_params)

                # Log execution time for performance monitoring
                execution_time = (time.time() - start_time) * 1000
                if execution_time > 1000:  # Target: <1000ms
                    logger.warning(f"⚠️ [PERFORMANCE] Order execution took {execution_time:.1f}ms (target: <1000ms)")
                else:
                    logger.info(f"⚡ [PERFORMANCE] Order executed in {execution_time:.1f}ms")

                # CRITICAL FIX: Log successful API response
                logger.info(f"✅ [BYBIT-API] Order response received: {response}")

            except Exception as e:
                logger.error(f"❌ [ORDER-EXECUTION] Order execution failed: {e}")
                logger.error(f"❌ [ORDER-EXECUTION] Order params were: {order_params}")

                # Return detailed error information
                return {
                    "success": False,
                    "error": f"Bybit API error: {str(e)}",
                    "exchange": self.name,
                    "order_params": order_params,
                    "execution_time_ms": (time.time() - start_time) * 1000
                }

            # CRITICAL FIX: Process response and provide detailed success/failure logging
            if response and response.get("retCode") == 0:
                order_id = response["result"]["orderId"]
                execution_time = (time.time() - start_time) * 1000  # Convert to milliseconds

                # CRITICAL SUCCESS LOGGING: Make it clear that a real trade was executed
                logger.warning(f"🎉 [REAL-MONEY-TRADE] ✅ BYBIT ORDER EXECUTED SUCCESSFULLY!")
                logger.warning(f"🎉 [REAL-MONEY-TRADE] Order ID: {order_id}")
                logger.warning(f"🎉 [REAL-MONEY-TRADE] Symbol: {normalized_symbol}")
                logger.warning(f"🎉 [REAL-MONEY-TRADE] Side: {side}")
                logger.warning(f"🎉 [REAL-MONEY-TRADE] Amount: {order_params.get('qty', 'N/A')}")
                logger.warning(f"🎉 [REAL-MONEY-TRADE] Price: {current_price}")
                logger.warning(f"🎉 [REAL-MONEY-TRADE] Execution Time: {execution_time:.1f}ms")
                logger.warning(f"🎉 [REAL-MONEY-TRADE] Exchange: {self.name}")
                logger.warning(f"🎉 [REAL-MONEY-TRADE] This was a REAL MONEY transaction!")

                print(f"\n🎉 [REAL-MONEY-TRADE] ✅ SUCCESSFUL TRADE EXECUTION!")
                print(f"🎉 [REAL-MONEY-TRADE] Order ID: {order_id}")
                print(f"🎉 [REAL-MONEY-TRADE] {side.upper()} {order_params.get('qty', 'N/A')} {normalized_symbol}")
                print(f"🎉 [REAL-MONEY-TRADE] Price: ${current_price}")
                print(f"🎉 [REAL-MONEY-TRADE] Exchange: {self.name}")
                print(f"🎉 [REAL-MONEY-TRADE] *** REAL MONEY TRANSACTION COMPLETED ***\n")

                # Log successful trade completion
                if trade_logger and trade_id:
                    try:
                        # Capture post-trade balances
                        post_trade_balances = {}
                        try:
                            post_trade_balances = await self.get_all_available_balances()
                        except Exception as e:
                            logger.warning(f"[TRADE-LOGGER] Could not capture post-trade balances: {e}")

                        # Calculate balance changes
                        balance_changes = {}
                        for currency in pre_trade_balances:
                            pre_balance = pre_trade_balances.get(currency, 0.0)
                            post_balance = post_trade_balances.get(currency, 0.0)
                            balance_changes[currency] = post_balance - pre_balance

                        completion_data = {
                            'post_trade_balances': post_trade_balances,
                            'balance_changes': balance_changes,
                            'execution_time_ms': execution_time,
                            'order_id': order_id,
                            'fill_price': current_price,
                            'status': 'filled',
                            'success': True,
                            'market_conditions': {
                                'timestamp': time.time(),
                                'price_at_execution': current_price
                            },
                            'notes': f"Order {order_id} executed successfully on {self.name}"
                        }

                        trade_logger.log_trade_completion(trade_id, completion_data)

                    except Exception as log_error:
                        logger.warning(f"[TRADE-LOGGER] Error logging trade completion: {log_error}")

                # CRITICAL FIX: Monitor order status instead of returning "submitted"
                order_status_result = await self._monitor_order_completion(order_id, normalized_symbol, timeout_seconds=30)

                return {
                    "order_id": order_id,
                    "symbol": normalized_symbol,
                    "side": side,
                    "type": order_type,
                    "status": order_status_result.get("status", "submitted"),
                    "filled_qty": order_status_result.get("filled_qty", "0"),
                    "avg_price": order_status_result.get("avg_price", current_price),
                    "exchange": self.name,
                    "execution_time_ms": execution_time,
                    "order_details": order_status_result
                }
            else:
                error_msg = response.get("retMsg", "Unknown error") if response else "No response"
                execution_time = (time.time() - start_time) * 1000
                logger.error(f"❌ Bybit order failed: {error_msg}")

                # Log failed trade
                if trade_logger and trade_id:
                    try:
                        completion_data = {
                            'execution_time_ms': execution_time,
                            'status': 'failed',
                            'success': False,
                            'error_message': error_msg,
                            'post_trade_balances': pre_trade_balances,  # No change
                            'balance_changes': {},
                            'notes': f"Order failed on {self.name}: {error_msg}"
                        }
                        trade_logger.log_trade_completion(trade_id, completion_data)

                        # Also log error separately
                        trade_logger.log_error({
                            'type': 'order_placement_failed',
                            'message': error_msg,
                            'exchange': self.name,
                            'symbol': normalized_symbol,
                            'context': {
                                'side': side,
                                'amount': amount,
                                'order_type': order_type,
                                'trade_id': trade_id
                            }
                        })

                    except Exception as log_error:
                        logger.warning(f"[TRADE-LOGGER] Error logging trade failure: {log_error}")

                return {"error": error_msg, "exchange": self.name}

        except Exception as e:
            execution_time = (time.time() - start_time) * 1000
            logger.error(f"❌ Bybit order placement failed: {e}")

            # Log exception
            if trade_logger and trade_id:
                try:
                    completion_data = {
                        'execution_time_ms': execution_time,
                        'status': 'error',
                        'success': False,
                        'error_message': str(e),
                        'post_trade_balances': pre_trade_balances,  # No change
                        'balance_changes': {},
                        'notes': f"Order exception on {self.name}: {str(e)}"
                    }
                    trade_logger.log_trade_completion(trade_id, completion_data)

                    # Also log error separately
                    trade_logger.log_error({
                        'type': 'order_placement_exception',
                        'message': str(e),
                        'exchange': self.name,
                        'symbol': normalized_symbol,
                        'context': {
                            'side': side,
                            'amount': amount,
                            'order_type': order_type,
                            'trade_id': trade_id
                        },
                        'stack_trace': str(e)
                    })

                except Exception as log_error:
                    logger.warning(f"[TRADE-LOGGER] Error logging trade exception: {log_error}")

            return {"error": str(e), "exchange": self.name}

    async def create_market_buy_order(self, symbol: str, amount: float) -> dict:
        """Create a market buy order"""
        return await self.place_order(symbol, "buy", amount, order_type="market")

    async def create_market_sell_order(self, symbol: str, amount: float) -> dict:
        """Create a market sell order"""
        return await self.place_order(symbol, "sell", amount, order_type="market")

    def cancel_order(self, symbol: str, order_id: str) -> dict:
        """Cancel an order"""
        try:
            normalized_symbol = self._normalize_symbol(symbol)
            response = self.session.cancel_order(
                category="spot",
                symbol=normalized_symbol,
                orderId=order_id
            )

            if response and response.get("retCode") == 0:
                logger.info(f"✅ Bybit order {order_id} cancelled")
                return {"status": "cancelled", "order_id": order_id}
            else:
                error_msg = response.get("retMsg", "Unknown error")
                logger.error(f"❌ Bybit cancel failed: {error_msg}")
                return {"error": error_msg}

        except Exception as e:
            logger.error(f"❌ Bybit cancel order failed: {e}")
            return {"error": str(e)}

    def discover_available_markets(self) -> Dict[str, List[str]]:
        """Discover all available markets and currencies dynamically"""
        try:
            logger.info("[DYNAMIC-DISCOVERY] Starting market discovery...")

            # Check if market discovery is available
            if not self.currency_manager.market_discovery:
                logger.warning("[DYNAMIC-DISCOVERY] Market discovery not initialized, using base currencies")
                return {}

            # Discover all available currencies
            currencies = self.currency_manager.market_discovery.discover_available_currencies()

            # Update supported currencies list
            self.currency_manager.supported_currencies = list(set(
                self.currency_manager.base_supported_currencies + currencies
            ))

            # Discover trading pairs for major currencies
            market_data = {}
            major_currencies = ['USDT', 'USD', 'EUR', 'BTC', 'ETH', 'SOL', 'BNB']

            for currency in major_currencies:
                if currency in currencies:
                    pairs = self.currency_manager.market_discovery.discover_trading_pairs_for_currency(currency)
                    market_data[currency] = pairs

            logger.info(f"[DYNAMIC-DISCOVERY] Discovered markets for {len(market_data)} major currencies")
            logger.info(f"[DYNAMIC-DISCOVERY] Total supported currencies: {len(self.currency_manager.supported_currencies)}")

            return market_data

        except Exception as e:
            logger.error(f"[DYNAMIC-DISCOVERY] Error discovering markets: {e}")
            return {}

    def get_optimal_trading_pairs_for_balance(self, available_currencies: List[str]) -> List[str]:
        """Get optimal trading pairs based on available balance currencies"""
        try:
            optimal_pairs = []

            # Check if market discovery is available
            if not self.currency_manager.market_discovery:
                logger.warning("[DYNAMIC-DISCOVERY] Market discovery not available, using fallback pairs")
                # Return common pairs as fallback
                fallback_pairs = []
                for currency in available_currencies:
                    if currency != 'USDT':
                        fallback_pairs.append(f"{currency}USDT")
                return fallback_pairs[:10]

            for currency in available_currencies:
                # Get trading pairs for this currency
                pairs = self.currency_manager.market_discovery.discover_trading_pairs_for_currency(currency)

                # Filter for high-liquidity pairs (typically with USDT, USD, BTC, ETH)
                high_liquidity_quotes = ['USDT', 'USD', 'BTC', 'ETH', 'EUR']

                for pair in pairs:
                    instrument_info = self.currency_manager.market_discovery.get_instrument_info(pair)
                    quote_coin = instrument_info.get('quoteCoin', '')

                    if quote_coin in high_liquidity_quotes:
                        optimal_pairs.append(pair)

            # Remove duplicates and sort by priority
            optimal_pairs = list(set(optimal_pairs))

            # Sort by quote currency priority (USDT first, then USD, etc.)
            quote_priority = {'USDT': 0, 'USD': 1, 'EUR': 2, 'BTC': 3, 'ETH': 4}

            def get_priority(pair):
                for quote, priority in quote_priority.items():
                    if pair.endswith(quote):
                        return priority
                return 999

            optimal_pairs.sort(key=get_priority)

            logger.info(f"[DYNAMIC-DISCOVERY] Found {len(optimal_pairs)} optimal trading pairs")
            return optimal_pairs[:20]  # Return top 20 pairs

        except Exception as e:
            logger.error(f"[DYNAMIC-DISCOVERY] Error finding optimal pairs: {e}")
            return []

    def refresh_market_data(self) -> bool:
        """Refresh market discovery data"""
        try:
            logger.info("[DYNAMIC-DISCOVERY] Refreshing market data...")

            # Discover markets
            market_data = self.discover_available_markets()

            # Update currency manager with fresh data
            if market_data:
                logger.info("[DYNAMIC-DISCOVERY] Market data refresh successful")
                return True
            else:
                logger.warning("[DYNAMIC-DISCOVERY] Market data refresh returned empty results")
                return False

        except Exception as e:
            logger.error(f"[DYNAMIC-DISCOVERY] Error refreshing market data: {e}")
            return False

    async def get_adaptive_position_sizes(self) -> Dict:
        """Get adaptive position sizes for all available balances"""
        try:
            logger.info("[ADAPTIVE-BALANCE] Calculating adaptive position sizes...")

            # Get all available balances
            all_balances = await self.get_all_available_balances()

            if not all_balances:
                logger.warning("[ADAPTIVE-BALANCE] No balances found for position sizing")
                return {}

            # Calculate adaptive position sizes
            position_analysis = self.currency_manager.balance_manager.get_optimal_position_sizes_for_portfolio(
                all_balances
            )

            logger.info(f"[ADAPTIVE-BALANCE] Position analysis completed for {len(all_balances)} currencies")

            # Log summary
            if '_portfolio_summary' in position_analysis:
                summary = position_analysis['_portfolio_summary']
                logger.info(f"[ADAPTIVE-BALANCE] Portfolio summary:")
                logger.info(f"  - Total USD value: ${summary['total_usd_value']:.2f}")
                logger.info(f"  - Active currencies: {summary['active_currencies']}")
                logger.info(f"  - Recommended exposure: ${summary['recommended_total_exposure']:.2f}")

            return position_analysis

        except Exception as e:
            logger.error(f"[ADAPTIVE-BALANCE] Error calculating adaptive position sizes: {e}")
            return {}

    async def get_recommended_trade_size(self, symbol: str, side: str,
                                       target_amount: Optional[float] = None) -> Dict:
        """Get recommended trade size using adaptive balance management"""
        try:
            normalized_symbol = self._normalize_symbol(symbol)
            base_currency, quote_currency = self.currency_manager.extract_base_quote_from_symbol(normalized_symbol)

            logger.info(f"[ADAPTIVE-TRADE] Getting recommended size for {side} {normalized_symbol}")

            # Get adaptive position sizes
            position_analysis = await self.get_adaptive_position_sizes()

            if side.lower() == 'buy':
                # For buy orders, use quote currency
                relevant_currency = quote_currency
            else:
                # For sell orders, use base currency
                relevant_currency = base_currency

            # Get position sizing for relevant currency
            if relevant_currency in position_analysis:
                position_info = position_analysis[relevant_currency]
                recommended_size = position_info['position_size']

                # CRITICAL FIX: For sell orders, ensure we never exceed max sellable amount
                if side.lower() == 'sell':
                    available_balance = position_info['available_balance']
                    max_sellable = available_balance * 0.85  # Use 85% as max sellable
                    recommended_size = min(recommended_size, max_sellable)
                    logger.info(f"[ADAPTIVE-SELL] Limited to max sellable: {recommended_size:.6f} {relevant_currency}")

                # If target amount specified, use smaller of target or recommended
                if target_amount is not None:
                    final_size = min(target_amount, recommended_size)
                    size_source = 'target_limited' if final_size == target_amount else 'adaptive_limited'
                else:
                    final_size = recommended_size
                    size_source = 'adaptive'

                return {
                    'recommended_size': final_size,
                    'size_source': size_source,
                    'currency': relevant_currency,
                    'available_balance': position_info['available_balance'],
                    'position_percentage': position_info['position_percentage'],
                    'balance_category': position_info['balance_category'],
                    'adaptive_factors': position_info.get('adaptive_factors', {}),
                    'target_amount': target_amount,
                    'max_recommended': recommended_size
                }
            else:
                # Fallback if currency not in analysis
                logger.warning(f"[ADAPTIVE-TRADE] Currency {relevant_currency} not found in position analysis")

                # Get balance directly
                balance = float(await self.get_balance(relevant_currency))
                fallback_size = balance * 0.2  # 20% fallback

                if target_amount is not None:
                    final_size = min(target_amount, fallback_size)
                else:
                    final_size = fallback_size

                return {
                    'recommended_size': final_size,
                    'size_source': 'fallback',
                    'currency': relevant_currency,
                    'available_balance': balance,
                    'position_percentage': 20.0,
                    'balance_category': 'unknown',
                    'target_amount': target_amount,
                    'max_recommended': fallback_size
                }

        except Exception as e:
            logger.error(f"[ADAPTIVE-TRADE] Error getting recommended trade size: {e}")
            return {
                'recommended_size': target_amount or 10.0,  # Conservative fallback
                'size_source': 'error_fallback',
                'error': str(e)
            }

    async def _monitor_order_completion(self, order_id: str, symbol: str, timeout_seconds: int = 30) -> dict:
        """Monitor order completion with timeout and status verification"""
        import asyncio
        import time

        start_time = time.time()
        check_interval = 0.5  # Check every 500ms
        max_checks = int(timeout_seconds / check_interval)

        logger.info(f"🔍 [ORDER-MONITOR] Monitoring order {order_id} for completion...")

        for attempt in range(max_checks):
            try:
                # Check order status using get_order_history
                order_status = self.session.get_order_history(category="spot", orderId=order_id)

                if order_status.get('retCode') == 0 and order_status['result']['list']:
                    order_info = order_status['result']['list'][0]
                    status = order_info.get('orderStatus', 'Unknown')
                    filled_qty = order_info.get('cumExecQty', '0')
                    avg_price = order_info.get('avgPrice', '0')

                    logger.debug(f"🔍 [ORDER-MONITOR] Attempt {attempt + 1}: Status={status}, Filled={filled_qty}")

                    # Check if order is completed
                    if status in ['Filled', 'PartiallyFilled']:
                        execution_time = (time.time() - start_time) * 1000
                        logger.info(f"✅ [ORDER-MONITOR] Order {order_id} completed: {status} in {execution_time:.1f}ms")

                        return {
                            "status": "filled" if status == 'Filled' else "partially_filled",
                            "filled_qty": filled_qty,
                            "avg_price": avg_price,
                            "order_status": status,
                            "execution_time_ms": execution_time,
                            "monitoring_attempts": attempt + 1
                        }

                    elif status in ['Cancelled', 'Rejected']:
                        logger.warning(f"⚠️ [ORDER-MONITOR] Order {order_id} failed: {status}")
                        return {
                            "status": "failed",
                            "order_status": status,
                            "filled_qty": filled_qty,
                            "avg_price": avg_price,
                            "error": f"Order {status.lower()}",
                            "monitoring_attempts": attempt + 1
                        }

                    # Order still pending, continue monitoring
                    elif status in ['New', 'PartiallyFilled']:
                        if attempt < max_checks - 1:  # Don't sleep on last attempt
                            await asyncio.sleep(check_interval)
                        continue

                    else:
                        logger.warning(f"⚠️ [ORDER-MONITOR] Unknown order status: {status}")
                        if attempt < max_checks - 1:
                            await asyncio.sleep(check_interval)
                        continue

                else:
                    # FIXED: Empty list means order was filled and removed (normal for IOC orders)
                    if order_status.get('retCode') == 0 and not order_status['result']['list']:
                        execution_time = (time.time() - start_time) * 1000
                        logger.info(f"✅ [ORDER-MONITOR] Order {order_id} completed: Filled in {execution_time:.1f}ms")
                        return {
                            "status": "filled",
                            "filled_qty": "unknown",  # Order completed but details not available
                            "avg_price": "unknown",
                            "order_status": "Filled",
                            "execution_time_ms": execution_time,
                            "monitoring_attempts": attempt + 1,
                            "note": "Order filled and removed from active orders (IOC behavior)"
                        }
                    else:
                        logger.debug(f"🔍 [ORDER-MONITOR] No order data available: {order_status}")
                        if attempt < max_checks - 1:
                            await asyncio.sleep(check_interval)
                        continue

            except Exception as e:
                logger.warning(f"⚠️ [ORDER-MONITOR] Error checking order status: {e}")
                if attempt < max_checks - 1:
                    await asyncio.sleep(check_interval)
                continue

        # Timeout reached
        logger.warning(f"⏰ [ORDER-MONITOR] Order {order_id} monitoring timeout after {timeout_seconds}s")
        return {
            "status": "timeout",
            "error": f"Order monitoring timeout after {timeout_seconds} seconds",
            "monitoring_attempts": max_checks,
            "filled_qty": "0",
            "avg_price": "0"
        }

    def get_order_history(self, symbol: str = None, limit: int = 50) -> dict:
        """Get order history"""
        try:
            params = {
                "category": "spot",
                "limit": limit
            }

            if symbol:
                params["symbol"] = self._normalize_symbol(symbol)

            response = self.session.get_order_history(**params)

            if response and response.get("retCode") == 0:
                return response["result"]
            else:
                error_msg = response.get("retMsg", "Unknown error")
                logger.error(f"❌ Bybit order history failed: {error_msg}")
                return {"error": error_msg}

        except Exception as e:
            logger.error(f"❌ Bybit get order history failed: {e}")
            return {"error": str(e)}

    def get_open_orders(self, symbol: str = None) -> dict:
        """Get open orders"""
        try:
            params = {"category": "spot"}

            if symbol:
                params["symbol"] = self._normalize_symbol(symbol)

            response = self.session.get_open_orders(**params)

            if response and response.get("retCode") == 0:
                return response["result"]
            else:
                error_msg = response.get("retMsg", "Unknown error")
                logger.error(f"❌ Bybit open orders failed: {error_msg}")
                return {"error": error_msg}

        except Exception as e:
            logger.error(f"❌ Bybit get open orders failed: {e}")
            return {"error": str(e)}

    def get_kline_data(self, symbol: str, interval: str = "1h", limit: int = 200) -> dict:
        """Get kline/candlestick data"""
        try:
            normalized_symbol = self._normalize_symbol(symbol)
            response = self.session.get_kline(
                category="spot",
                symbol=normalized_symbol,
                interval=interval,
                limit=limit
            )

            if response and response.get("retCode") == 0:
                return response["result"]
            else:
                error_msg = response.get("retMsg", "Unknown error")
                logger.error(f"❌ Bybit kline data failed: {error_msg}")
                return {"error": error_msg}

        except Exception as e:
            logger.error(f"❌ Bybit get kline data failed: {e}")
            return {"error": str(e)}

    def get_orderbook(self, symbol: str, limit: int = 25) -> dict:
        """Get orderbook depth"""
        try:
            normalized_symbol = self._normalize_symbol(symbol)
            response = self.session.get_orderbook(
                category="spot",
                symbol=normalized_symbol,
                limit=limit
            )

            if response and response.get("retCode") == 0:
                return response["result"]
            else:
                error_msg = response.get("retMsg", "Unknown error")
                logger.error(f"❌ Bybit orderbook failed: {error_msg}")
                return {"error": error_msg}

        except Exception as e:
            logger.error(f"❌ Bybit get orderbook failed: {e}")
            return {"error": str(e)}

    def get_trade_history(self, symbol: str = None, limit: int = 50) -> dict:
        """Get trade execution history"""
        try:
            params = {
                "category": "spot",
                "limit": limit
            }

            if symbol:
                params["symbol"] = self._normalize_symbol(symbol)

            response = self.session.get_executions(**params)

            if response and response.get("retCode") == 0:
                return response["result"]
            else:
                error_msg = response.get("retMsg", "Unknown error")
                logger.error(f"❌ Bybit trade history failed: {error_msg}")
                return {"error": error_msg}

        except Exception as e:
            logger.error(f"❌ Bybit get trade history failed: {e}")
            return {"error": str(e)}

    def get_ticker(self, symbol: str) -> Optional[dict]:
        """Get ticker data in a format compatible with the main trading system"""
        try:
            if not self.session:
                logger.error("Bybit session not initialized")
                return None

            normalized_symbol = self._normalize_symbol(symbol)
            response = self.session.get_tickers(
                category="spot", symbol=normalized_symbol
            )

            if response and response["retCode"] == 0 and response["result"]["list"]:
                ticker_data = response["result"]["list"][0]

                # Convert to standard ticker format
                last_price = float(ticker_data.get("lastPrice", 0))
                prev_price = float(ticker_data.get("prevPrice24h", last_price))
                change = last_price - prev_price
                percentage = (change / prev_price * 100) if prev_price > 0 else 0

                return {
                    'symbol': symbol,
                    'last': last_price,
                    'close': last_price,
                    'bid': float(ticker_data.get("bid1Price", 0)),
                    'ask': float(ticker_data.get("ask1Price", 0)),
                    'high': float(ticker_data.get("highPrice24h", 0)),
                    'low': float(ticker_data.get("lowPrice24h", 0)),
                    'volume': float(ticker_data.get("volume24h", 0)),
                    'baseVolume': float(ticker_data.get("volume24h", 0)),
                    'change': change,
                    'percentage': percentage,
                    'open': prev_price,
                    'timestamp': int(ticker_data.get("time", 0)),
                    'datetime': None,
                    'info': ticker_data
                }
            else:
                logger.warning(f"No ticker data for {normalized_symbol}")
                return None

        except Exception as e:
            logger.error(f"Error getting ticker for {symbol}: {e}")
            return None

    def fetch_ticker(self, symbol: str) -> Optional[dict]:
        """Alias for get_ticker to support both naming conventions"""
        return self.get_ticker(symbol)

    def get_instruments_info(self, symbol: Optional[str] = None) -> dict:
        """Get detailed instrument information"""
        try:
            if not self.session:
                return {"error": "Session not initialized"}

            params = {"category": "spot"}

            if symbol:
                params["symbol"] = self._normalize_symbol(symbol)

            response = self.session.get_instruments_info(**params)

            if response and response.get("retCode") == 0:
                return response["result"]
            else:
                error_msg = response.get("retMsg", "Unknown error")
                logger.error(f"❌ Bybit instruments info failed: {error_msg}")
                return {"error": error_msg}

        except Exception as e:
            logger.error(f"❌ Bybit get instruments info failed: {e}")
            return {"error": str(e)}

    def get_all_trading_pairs(self) -> dict:
        """Get all available trading pairs with their specifications - DYNAMIC DISCOVERY"""
        try:
            if not self.session:
                return {"error": "Session not initialized"}

            # Fetch all spot trading instruments
            response = self.session.get_instruments_info(category="spot")

            if response and response.get("retCode") == 0:
                instruments = response["result"]["list"]
                trading_pairs = {}

                for instrument in instruments:
                    symbol = instrument.get("symbol")
                    if symbol:
                        # Parse base and quote currencies
                        base_currency, quote_currency = self._parse_trading_pair(symbol)

                        # Get instrument specifications
                        status = instrument.get("status", "")
                        min_order_qty = float(instrument.get("lotSizeFilter", {}).get("minOrderQty", "0"))
                        min_order_amt = float(instrument.get("lotSizeFilter", {}).get("minOrderAmt", "0"))

                        # Only include active trading pairs with reasonable minimums
                        if (status.lower() in ["trading", "active"] and
                            min_order_qty > 0 and min_order_amt > 0):

                            trading_pairs[symbol] = {
                                'base_currency': base_currency,
                                'quote_currency': quote_currency,
                                'status': status.lower(),
                                'min_order_qty': min_order_qty,
                                'min_order_amt': min_order_amt,
                                'symbol_bybit': symbol,  # Native Bybit format
                                'symbol_user': f"{base_currency}-{quote_currency}"  # User-friendly format
                            }

                logger.info(f"🔍 [DYNAMIC-DISCOVERY] Found {len(trading_pairs)} trading pairs on Bybit")
                return {"trading_pairs": trading_pairs, "count": len(trading_pairs)}
            else:
                error_msg = response.get("retMsg", "Unknown error")
                logger.error(f"❌ Bybit trading pairs discovery failed: {error_msg}")
                return {"error": error_msg}

        except Exception as e:
            logger.error(f"❌ Bybit get all trading pairs failed: {e}")
            return {"error": str(e)}

    def _parse_trading_pair(self, symbol: str) -> tuple:
        """Parse trading pair into base and quote currencies - UNIVERSAL PARSER"""
        try:
            # Common quote currencies in order of priority
            quote_currencies = ['USDT', 'USDC', 'BTC', 'ETH', 'USD', 'EUR', 'GBP', 'JPY']

            for quote in quote_currencies:
                if symbol.endswith(quote):
                    base = symbol[:-len(quote)]
                    if base:  # Ensure base currency is not empty
                        return base, quote

            # Fallback: assume last 3-4 characters are quote currency
            if len(symbol) > 4:
                return symbol[:-4], symbol[-4:]
            elif len(symbol) > 3:
                return symbol[:-3], symbol[-3:]
            else:
                return symbol, "UNKNOWN"

        except Exception as e:
            logger.debug(f"Error parsing trading pair {symbol}: {e}")
            return symbol, "UNKNOWN"

    def get_minimum_order_requirements(self, symbol: str) -> dict:
        """Get minimum order requirements for a specific trading pair"""
        try:
            normalized_symbol = self._normalize_symbol(symbol)
            response = self.session.get_instruments_info(category="spot", symbol=normalized_symbol)

            if response and response.get("retCode") == 0 and response["result"]["list"]:
                instrument = response["result"]["list"][0]
                lot_size_filter = instrument.get("lotSizeFilter", {})
                price_filter = instrument.get("priceFilter", {})

                return {
                    "symbol": normalized_symbol,
                    "min_order_qty": float(lot_size_filter.get("minOrderQty", 0)),
                    "max_order_qty": float(lot_size_filter.get("maxOrderQty", 0)),
                    "min_order_amt": float(lot_size_filter.get("minOrderAmt", 0)),
                    "max_order_amt": float(lot_size_filter.get("maxOrderAmt", 0)),
                    "qty_step": float(lot_size_filter.get("qtyStep", 0)),
                    "tick_size": float(price_filter.get("tickSize", 0)),
                    "min_price": float(price_filter.get("minPrice", 0)),
                    "max_price": float(price_filter.get("maxPrice", 0))
                }
            else:
                return {"error": f"Could not get requirements for {symbol}"}

        except Exception as e:
            logger.error(f"Error getting minimum order requirements for {symbol}: {e}")
            return {"error": str(e)}

    def is_trading_pair_supported(self, symbol: str) -> bool:
        """Check if a trading pair is supported on Bybit"""
        try:
            normalized_symbol = self._normalize_symbol(symbol)
            response = self.session.get_instruments_info(category="spot", symbol=normalized_symbol)

            if response and response.get("retCode") == 0 and response["result"]["list"]:
                instrument = response["result"]["list"][0]
                status = instrument.get("status", "").lower()
                return status == "trading"

            return False

        except Exception as e:
            logger.debug(f"Error checking if {symbol} is supported: {e}")
            return False

    def get_all_balances_detailed(self) -> dict:
        """Get detailed balances for all currencies - CURRENCY AGNOSTIC"""
        try:
            if not self.session:
                return {"error": "Session not initialized"}

            # Get wallet balance for all coins
            response = self.session.get_wallet_balance(accountType="UNIFIED")

            if response and response.get("retCode") == 0:
                balances = {}

                for account in response["result"]["list"]:
                    for coin_info in account.get("coin", []):
                        coin = coin_info.get("coin")
                        if coin:
                            balances[coin] = {
                                "free": float(coin_info.get("availableToWithdraw", 0)),
                                "used": float(coin_info.get("locked", 0)),
                                "total": float(coin_info.get("walletBalance", 0)),
                                "usd_value": float(coin_info.get("usdValue", 0))
                            }

                logger.debug(f"💰 [ALL-BALANCES] Found balances for {len(balances)} currencies")
                return balances
            else:
                error_msg = response.get("retMsg", "Unknown error")
                logger.error(f"❌ Bybit get all balances failed: {error_msg}")
                return {"error": error_msg}

        except Exception as e:
            logger.error(f"❌ Bybit get all balances detailed failed: {e}")
            return {"error": str(e)}

    async def place_market_buy_order(self, symbol: str, amount: float) -> dict:
        """Place a market buy order"""
        return await self.place_order(
            symbol=symbol,
            side="Buy",
            amount=amount,
            order_type="Market"
        )

    async def place_market_sell_order(self, symbol: str, amount: float) -> dict:
        """Place a market sell order"""
        return await self.place_order(
            symbol=symbol,
            side="Sell",
            amount=amount,
            order_type="Market"
        )

    async def place_limit_buy_order(self, symbol: str, amount: float, price: float) -> dict:
        """Place a limit buy order"""
        return await self.place_order(
            symbol=symbol,
            side="Buy",
            amount=amount,
            price=price,
            order_type="Limit"
        )

    async def place_limit_sell_order(self, symbol: str, amount: float, price: float) -> dict:
        """Place a limit sell order"""
        return await self.place_order(
            symbol=symbol,
            side="Sell",
            amount=amount,
            price=price,
            order_type="Limit"
        )

    def close(self):
        """Clean close method."""
        logger.info("BybitClientFixed: Session closed.")
        pass