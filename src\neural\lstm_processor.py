"""
LSTM Processor for Trading System
Simple implementation to resolve import errors
"""

import logging
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class LSTMPrediction:
    """LSTM prediction result"""
    prediction: float
    confidence: float
    features_used: int
    processing_time_ms: float

class LSTMProcessor:
    """Enhanced LSTM processor for trading predictions with sentiment integration"""

    def __init__(self, input_size: int = 30, hidden_size: int = 128, num_layers: int = 3):
        self.input_size = input_size  # Increased to handle enhanced features
        self.hidden_size = hidden_size  # Increased for better capacity
        self.num_layers = num_layers  # Increased for deeper learning
        self.is_trained = False

        # Feature group indices for enhanced processing
        self.feature_groups = {
            'price_features': slice(0, 8),      # Price and momentum features
            'technical_features': slice(8, 13), # Technical indicators
            'sentiment_features': slice(13, 16), # Basic sentiment
            'risk_features': slice(16, 18),     # Risk features
            'time_features': slice(18, 22),     # Time-based features
            'fear_greed_features': slice(22, 26), # Fear & Greed Index features
            'enhanced_sentiment_features': slice(26, 30) # Enhanced sentiment features
        }

        logger.info(f"[LSTM] Initialized enhanced LSTM processor: input={input_size}, hidden={hidden_size}, layers={num_layers}")
        logger.info(f"[LSTM] Feature groups configured for enhanced sentiment analysis")
    
    def predict(self, features: Dict[str, float]) -> LSTMPrediction:
        """Make enhanced prediction using LSTM model with sentiment integration"""
        try:
            import time
            start_time = time.time()

            # Convert features to array with proper ordering
            feature_values = self._prepare_feature_vector(features)

            # Enhanced prediction logic with sentiment integration
            feature_array = np.array(feature_values)

            # Extract feature groups for specialized processing
            price_features = feature_array[self.feature_groups['price_features']]
            technical_features = feature_array[self.feature_groups['technical_features']]
            sentiment_features = feature_array[self.feature_groups['sentiment_features']]
            risk_features = feature_array[self.feature_groups['risk_features']]
            fear_greed_features = feature_array[self.feature_groups['fear_greed_features']]
            enhanced_sentiment_features = feature_array[self.feature_groups['enhanced_sentiment_features']]

            # Calculate component scores
            momentum_score = self._calculate_momentum_score(price_features)
            technical_score = self._calculate_technical_score(technical_features)
            sentiment_score = self._calculate_sentiment_score(sentiment_features, fear_greed_features, enhanced_sentiment_features)
            risk_adjustment = self._calculate_risk_adjustment(risk_features)

            # Enhanced prediction algorithm with sentiment weighting
            base_prediction = (momentum_score * 0.4 + technical_score * 0.3 + sentiment_score * 0.3)

            # Apply risk adjustment
            prediction = base_prediction * risk_adjustment
            prediction = max(-0.15, min(0.25, prediction))  # Enhanced range for better opportunities

            # Enhanced confidence calculation with sentiment factors
            confidence = self._calculate_enhanced_confidence(feature_array, sentiment_score)
            
            processing_time = (time.time() - start_time) * 1000
            
            return LSTMPrediction(
                prediction=prediction,
                confidence=confidence,
                features_used=len(feature_values),
                processing_time_ms=processing_time
            )
            
        except Exception as e:
            logger.error(f"[LSTM] Error in prediction: {e}")
            return LSTMPrediction(
                prediction=0.0,
                confidence=0.5,
                features_used=0,
                processing_time_ms=0.0
            )
    
    def train(self, training_data: List[Dict[str, Any]]) -> bool:
        """Train the LSTM model (placeholder)"""
        try:
            logger.info(f"[LSTM] Training with {len(training_data)} samples")
            
            # Placeholder training logic
            if len(training_data) > 10:
                self.is_trained = True
                logger.info("[LSTM] Training completed successfully")
                return True
            else:
                logger.warning("[LSTM] Insufficient training data")
                return False
                
        except Exception as e:
            logger.error(f"[LSTM] Error in training: {e}")
            return False

    def train_on_data(self, symbol: str, price_data: List[float], volume_data: List[float] = None,
                     additional_features: Dict[str, List[float]] = None) -> bool:
        """Train the LSTM model on specific market data for a symbol"""
        try:
            logger.debug(f"[LSTM] Training on data for {symbol}: {len(price_data)} price points")

            # Convert market data to training format
            training_samples = []

            # Create training samples from price data
            for i in range(len(price_data)):
                sample = {
                    'symbol': symbol,
                    'price': price_data[i],
                    'volume': volume_data[i] if volume_data and i < len(volume_data) else 0.0,
                    'timestamp': i,  # Use index as timestamp
                }

                # Add additional features if provided
                if additional_features:
                    for feature_name, feature_data in additional_features.items():
                        if i < len(feature_data):
                            sample[feature_name] = feature_data[i]

                training_samples.append(sample)

            # Use existing train method
            success = self.train(training_samples)

            if success:
                logger.debug(f"[LSTM] Successfully trained on {len(training_samples)} samples for {symbol}")
            else:
                logger.warning(f"[LSTM] Training failed for {symbol}")

            return success

        except Exception as e:
            logger.error(f"[LSTM] Error in train_on_data for {symbol}: {e}")
            return False

    def get_model_info(self) -> Dict[str, Any]:
        """Get model information"""
        return {
            'input_size': self.input_size,
            'hidden_size': self.hidden_size,
            'num_layers': self.num_layers,
            'is_trained': self.is_trained,
            'model_type': 'Enhanced_LSTM_Processor'
        }

    def _prepare_feature_vector(self, features: Dict[str, float]) -> List[float]:
        """Prepare feature vector with proper ordering for enhanced processing"""
        try:
            # Define expected feature order for enhanced LSTM
            expected_features = [
                # Price features (8)
                'current_price', 'price_change_1h', 'price_change_24h', 'volume',
                'volume_change', 'volatility', 'momentum_1h', 'momentum_24h',
                # Technical indicators (5)
                'rsi', 'macd', 'macd_signal', 'bollinger_upper', 'bollinger_lower',
                # Sentiment features (3)
                'sentiment_score', 'sentiment_confidence', 'sentiment_strength',
                # Risk features (2)
                'risk_score', 'volatility_risk',
                # Time features (4)
                'hour_of_day', 'day_of_week', 'us_market_session', 'time_momentum_strength',
                # Fear & Greed Index features (4)
                'fear_greed_index', 'fear_greed_trend', 'fear_greed_momentum', 'market_regime_sentiment',
                # Enhanced sentiment features (4)
                'web_sentiment_score', 'web_sentiment_confidence', 'news_impact_score', 'composite_sentiment'
            ]

            # Extract features in proper order with defaults
            feature_values = []
            for feature_name in expected_features:
                if feature_name in features:
                    feature_values.append(features[feature_name])
                else:
                    # Provide sensible defaults
                    defaults = {
                        'current_price': 0.0, 'price_change_1h': 0.0, 'price_change_24h': 0.0,
                        'volume': 0.0, 'volume_change': 0.0, 'volatility': 0.1,
                        'momentum_1h': 0.0, 'momentum_24h': 0.0,
                        'rsi': 50.0, 'macd': 0.0, 'macd_signal': 0.0, 'bollinger_upper': 0.0, 'bollinger_lower': 0.0,
                        'sentiment_score': 0.0, 'sentiment_confidence': 0.0, 'sentiment_strength': 0.0,
                        'risk_score': 0.5, 'volatility_risk': 0.1,
                        'hour_of_day': 0.5, 'day_of_week': 0.5, 'us_market_session': 0.0, 'time_momentum_strength': 0.5,
                        'fear_greed_index': 0.65, 'fear_greed_trend': 0.0, 'fear_greed_momentum': 0.0, 'market_regime_sentiment': 0.5,
                        'web_sentiment_score': 0.0, 'web_sentiment_confidence': 0.0, 'news_impact_score': 0.0, 'composite_sentiment': 0.0
                    }
                    feature_values.append(defaults.get(feature_name, 0.0))

            return feature_values

        except Exception as e:
            logger.error(f"[LSTM] Error preparing feature vector: {e}")
            return [0.0] * self.input_size

    def _calculate_momentum_score(self, price_features: np.ndarray) -> float:
        """Calculate momentum score from price features"""
        try:
            if len(price_features) >= 6:
                # Use momentum features (indices 6-7 in price group)
                momentum_1h = price_features[6] if len(price_features) > 6 else 0.0
                momentum_24h = price_features[7] if len(price_features) > 7 else 0.0

                # Weight recent momentum more heavily
                momentum_score = momentum_1h * 0.7 + momentum_24h * 0.3
                return max(-1.0, min(1.0, momentum_score))
            return 0.0
        except Exception as e:
            logger.error(f"[LSTM] Error calculating momentum score: {e}")
            return 0.0

    def _calculate_technical_score(self, technical_features: np.ndarray) -> float:
        """Calculate technical analysis score"""
        try:
            if len(technical_features) >= 5:
                rsi = technical_features[0] / 100.0 if technical_features[0] > 1 else technical_features[0]
                macd = technical_features[1]
                macd_signal = technical_features[2]

                # RSI scoring (oversold/overbought)
                rsi_score = 0.0
                if rsi < 0.3:  # Oversold - potential buy
                    rsi_score = 0.3
                elif rsi > 0.7:  # Overbought - potential sell
                    rsi_score = -0.3

                # MACD scoring
                macd_score = (macd - macd_signal) * 0.1  # Normalize

                return max(-1.0, min(1.0, rsi_score + macd_score))
            return 0.0
        except Exception as e:
            logger.error(f"[LSTM] Error calculating technical score: {e}")
            return 0.0

    def _calculate_sentiment_score(self, sentiment_features: np.ndarray,
                                 fear_greed_features: np.ndarray,
                                 enhanced_sentiment_features: np.ndarray) -> float:
        """Calculate enhanced sentiment score with Fear & Greed Index"""
        try:
            total_score = 0.0
            weight_sum = 0.0

            # Basic sentiment (weight: 0.3)
            if len(sentiment_features) >= 3:
                base_sentiment = sentiment_features[0]
                confidence = sentiment_features[1]
                strength = sentiment_features[2]

                weighted_sentiment = base_sentiment * confidence * strength
                total_score += weighted_sentiment * 0.3
                weight_sum += 0.3

            # Fear & Greed sentiment (weight: 0.4)
            if len(fear_greed_features) >= 4:
                fear_greed_index = fear_greed_features[0]
                fear_greed_trend = fear_greed_features[1]
                market_regime = fear_greed_features[3]

                # Convert Fear & Greed to sentiment score
                fg_sentiment = (fear_greed_index - 0.5) * 2  # Convert 0-1 to -1 to 1
                fg_score = fg_sentiment * 0.7 + fear_greed_trend * 0.3

                total_score += fg_score * 0.4
                weight_sum += 0.4

            # Enhanced web sentiment (weight: 0.3)
            if len(enhanced_sentiment_features) >= 4:
                web_sentiment = enhanced_sentiment_features[0]
                web_confidence = enhanced_sentiment_features[1]
                composite_sentiment = enhanced_sentiment_features[3]

                enhanced_score = composite_sentiment * 0.6 + web_sentiment * web_confidence * 0.4
                total_score += enhanced_score * 0.3
                weight_sum += 0.3

            # Normalize by total weight
            if weight_sum > 0:
                return max(-1.0, min(1.0, total_score / weight_sum))
            return 0.0

        except Exception as e:
            logger.error(f"[LSTM] Error calculating sentiment score: {e}")
            return 0.0

    def _calculate_risk_adjustment(self, risk_features: np.ndarray) -> float:
        """Calculate risk adjustment factor"""
        try:
            if len(risk_features) >= 2:
                risk_score = risk_features[0]
                volatility_risk = risk_features[1]

                # Higher risk = lower adjustment factor
                risk_adjustment = 1.0 - (risk_score * 0.5 + volatility_risk * 0.3)
                return max(0.3, min(1.0, risk_adjustment))  # Keep between 30% and 100%
            return 0.8  # Default moderate risk adjustment
        except Exception as e:
            logger.error(f"[LSTM] Error calculating risk adjustment: {e}")
            return 0.8

    def _calculate_enhanced_confidence(self, feature_array: np.ndarray, sentiment_score: float) -> float:
        """Calculate enhanced confidence with sentiment factors"""
        try:
            # Base confidence from feature completeness
            feature_completeness = len(feature_array) / self.input_size
            base_confidence = 0.5 + feature_completeness * 0.3

            # Sentiment confidence boost
            sentiment_confidence_boost = abs(sentiment_score) * 0.1

            # Feature quality assessment
            non_zero_features = np.count_nonzero(feature_array)
            feature_quality = non_zero_features / len(feature_array)

            # Combined confidence
            total_confidence = base_confidence + sentiment_confidence_boost + feature_quality * 0.1

            return max(0.3, min(0.95, total_confidence))

        except Exception as e:
            logger.error(f"[LSTM] Error calculating enhanced confidence: {e}")
            return 0.6

# Export for compatibility
__all__ = ['LSTMProcessor', 'LSTMPrediction']
